<div ng-controller = "RuleList" class="page">
    <div class="row ui-section mb-0">
        <div class="col-md-12">
            <article class="article">

                <div class="body_inner_content">

                    <md-card class="no-margin-h pt-0">
                        <md-toolbar class="md-table-toolbar md-default">
                            <div class="md-toolbar-tools">
                                <span>Adjust Version</span>
                                <div flex></div>
                                <md-button class="md-button md-raised btn-w-md md-default" style="display: flex;" ng-click="newversionpannel = !newversionpannel">
                                    <i class="material-icons">add</i> Create New Version
                                </md-button>
                            </div>
                        </md-toolbar>

                        <div class="row">

                            <div class="col-md-12"  ng-show="newversionpannel">
                                <div class="col-md-12 bg-grey-light mb-10" style="padding:5px 0px 0px 0px;">

                                    <div class="col-md-3">
                                        <md-input-container class="md-block">
                                            <label>Version Name</label>
                                            <input type="text" name="version_name"  ng-model="version_name"  required ng-maxlength="45" />
                                        </md-input-container>
                                    </div>
                                    <div class="col-md-6">
                                        <md-input-container class="md-block">
                                            <label>Description</label>
                                            <input type="text" name="version_description"  ng-model="version_description" />
                                        </md-input-container>
                                    </div>
                                    <div class="col-md-3">
                                        <button type="button" class="md-button md-raised md-primary mb-10" style=" margin-top: 10px;" ng-disabled="!version_name" ng-click="CreateNewBRVersion($event)">Create New Version</button>
                                    </div>

                                </div>
                            </div>

                            <div class="col-md-12">
                                <div class="col-md-3 col-sm-4">
                                    <md-input-container class="md-block">
                                        <label>Version Name</label>
                                        <md-select name="version_id" ng-model="version_id" required ng-change="BREVersionChanged()">
                                            <md-option value="{{rv.version_id}}" ng-repeat="rv in RuleVersions">{{rv.version_name}} <span style="color:red;" ng-show="rv.current_version == '1'">(Current Version)</span><span style="color:orange;" ng-show="rv.previous_version == '1'"> (Previous Version)</span> </md-option>
                                        </md-select>
                                    </md-input-container>
                                </div>
                                <div class="col-md-2  col-sm-4">
                                    <label>Version Status</label>
                                    <p><strong>{{GetVersionStatus()}}</strong></p>
                                </div>
                                <div class="col-md-2 col-sm-4" ng-show="GetVersionStatus() == 'Pending'" >
                                    <label>Update Status To</label>
                                    <p><md-switch aria-label="default" ng-model="change_status" class="md-default" ng-change="MakeVersionActive($event)">Active</md-switch> </p>
                                </div>
                                <div class="col-md-3  col-sm-4" ng-show="canShowMakeCurrentVersion()">
                                    <button type="button" class="md-button md-raised md-primary" style=" margin-top: 10px;" ng-disabled="!version_id" ng-click="MakeCurrentVersion($event)">Make it Current Version</button>
                                </div>

                            </div>

                        </div>
                    </md-card>

                    <md-card class="no-margin-h pt-0">

                        <md-toolbar class="md-table-toolbar md-default" ng-init="RuleList = true;">
                            <div class="md-toolbar-tools">
                                <i ng-click="RuleList = !RuleList" class="material-icons md-primary" ng-show="RuleList">keyboard_arrow_up</i>
                                <i ng-click="RuleList = !RuleList" class="material-icons md-primary" ng-show="! RuleList">keyboard_arrow_down</i>
                                <span ng-click="RuleList = !RuleList">List of Rules</span>
                                <div flex></div>

                                <a href="#" ng-click="showExportDialog($event)" class="md-button md-raised btn-w-md md-default dis_none_v" style="display: flex; margin-right: 5px;">
                                    <md-icon class="mr-5 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon> <span>Export to Excel</span>
                                </a>
                                <a href="#" ng-click="showExportDialog($event)" class="md-button md-raised md-default dis_open_v" style="display: none; margin-right: 5px; min-width: 40px;">
                                    <md-icon class="mt-10 excel_icon" md-svg-src="../assets/images/excel.svg" ></md-icon>
                                </a>

                                <a href="#!/BusinessRule" class="md-button md-raised btn-w-md md-default" style="display: flex;">
                                    <i class="material-icons">add</i> Create New Rule
                                </a>
                            </div>
                        </md-toolbar>

                        <div class="row"  ng-show="RuleList">
                            <div class="col-md-12">
                                <div class="col-md-12">

                                    <div class="tablemovebtns" style="top:70%;">
                                        <a class="md-button md-raised md-default" id="left-button"><i class="material-icons">keyboard_arrow_left</i></a>
                                    </div>
                                    <div class="tablemovebtns" style="top:70%;">
                                        <a class="md-button md-raised md-default" id="right-button"><i class="material-icons">keyboard_arrow_right</i></a>
                                    </div>

                                    <div ng-show="pagedItems" class="pull-right pageditems">
                                        <small>
                                        Showing Results <span style="font-weight:bold;">{{(currentPage * itemsPerPage) + 1}}</span>
                                        to <span style="font-weight:bold;" ng-show="total >= (currentPage * itemsPerPage) + itemsPerPage">{{(currentPage * itemsPerPage) + itemsPerPage}}</span>
                                            <span style="font-weight:bold;" ng-show="total < (currentPage * itemsPerPage) + itemsPerPage">{{total}}</span>
                                        of <span style="font-weight:bold;">{{total}}</span>
                                        </small>
                                    </div>
                                    <div style="clear:both;"></div>

                                    <div class="table-container table-responsive" style="overflow: auto;">


                                        <table class="table table-striped">

                                            <thead>

                                                <tr class="th_sorting">
                                                     <!-- 1. Edit -->
                                                     <th >Edit</th>

                                                    <!-- 2. Priority -->
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('priority')" ng-class="{'orderby' : OrderBy == 'priority'}">
                                                        <div>
                                                            Priority <i class="fa fa-sort pull-right" ng-show="OrderBy != 'priority'"></i>
                                                            <span ng-show="OrderBy == 'priority'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 3. Rule Name -->
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('rule_name')" ng-class="{'orderby' : OrderBy == 'rule_name'}">
                                                        <div>
                                                            Rule Name <i class="fa fa-sort pull-right" ng-show="OrderBy != 'rule_name'"></i>
                                                            <span ng-show="OrderBy == 'rule_name'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 4. Description -->
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('rule_description')" ng-class="{'orderby' : OrderBy == 'rule_description'}">
                                                        <div>
                                                            Description <i class="fa fa-sort pull-right" ng-show="OrderBy != 'rule_description'"></i>
                                                            <span ng-show="OrderBy == 'rule_description'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 5. Customer -->
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('CustomerName')" ng-class="{'orderby' : OrderBy == 'CustomerName'}">
                                                        <div>
                                                            Customer <i class="fa fa-sort pull-right" ng-show="OrderBy != 'CustomerName'"></i>
                                                            <span ng-show="OrderBy == 'CustomerName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 6. Facility -->
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('FacilityName')" ng-class="{'orderby' : OrderBy == 'FacilityName'}">
                                                        <div>
                                                            Facility <i class="fa fa-sort pull-right" ng-show="OrderBy != 'FacilityName'"></i>
                                                            <span ng-show="OrderBy == 'FacilityName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 7. Workflow -->
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('workflow')" ng-class="{'orderby' : OrderBy == 'workflow'}">
                                                        <div>
                                                            Workflow <i class="fa fa-sort pull-right" ng-show="OrderBy != 'workflow'"></i>
                                                            <span ng-show="OrderBy == 'workflow'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 8. Part Type -->
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('part_types')" ng-class="{'orderby' : OrderBy == 'part_types'}">
                                                        <div>
                                                            Part Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'part_types'"></i>
                                                            <span ng-show="OrderBy == 'part_types'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 9. Rule Summary -->
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('rule_summary')" ng-class="{'orderby' : OrderBy == 'rule_summary'}">
                                                        <div style="min-width: 280px;">
                                                            Rule Summary <i class="fa fa-sort pull-right" ng-show="OrderBy != 'rule_summary'"></i>
                                                            <span ng-show="OrderBy == 'rule_summary'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 10. Disposition -->
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('disposition')" ng-class="{'orderby' : OrderBy == 'disposition'}">
                                                        <div>
                                                            Disposition <i class="fa fa-sort pull-right" ng-show="OrderBy != 'disposition'"></i>
                                                            <span ng-show="OrderBy == 'disposition'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 11. Rule ID -->
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('rule_id_text')" ng-class="{'orderby' : OrderBy == 'rule_id_text'}">
                                                        <div style="min-width: 80px;">
                                                            Rule ID <i class="fa fa-sort pull-right" ng-show="OrderBy != 'rule_id_text'"></i>
                                                            <span ng-show="OrderBy == 'rule_id_text'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 12. Source Type -->
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('SourceTypeName')" ng-class="{'orderby' : OrderBy == 'SourceTypeName'}">
                                                        <div>
                                                            Source Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'SourceTypeName'"></i>
                                                            <span ng-show="OrderBy == 'SourceTypeName'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 13. Material Type -->
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('MaterialType')" ng-class="{'orderby' : OrderBy == 'MaterialType'}">
                                                        <div>
                                                            Material Type <i class="fa fa-sort pull-right" ng-show="OrderBy != 'MaterialType'"></i>
                                                            <span ng-show="OrderBy == 'MaterialType'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 14. Status -->
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('status')" ng-class="{'orderby' : OrderBy == 'status'}">
                                                        <div style="min-width: 80px;">
                                                            Status <i class="fa fa-sort pull-right" ng-show="OrderBy != 'status'"></i>
                                                            <span ng-show="OrderBy == 'status'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 15. Created Date -->
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('created_date')" ng-class="{'orderby' : OrderBy == 'created_date'}">
                                                        <div style="min-width: 120px;">
                                                            Created Date <i class="fa fa-sort pull-right" ng-show="OrderBy != 'created_date'"></i>
                                                            <span ng-show="OrderBy == 'created_date'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 16. Created By -->
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('created_by')" ng-class="{'orderby' : OrderBy == 'created_by'}">
                                                        <div style="min-width: 100px;">
                                                            Created By <i class="fa fa-sort pull-right" ng-show="OrderBy != 'created_by'"></i>
                                                            <span ng-show="OrderBy == 'created_by'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 17. Updated Date -->
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('updated_date')" ng-class="{'orderby' : OrderBy == 'updated_date'}">
                                                        <div style="min-width: 120px;">
                                                            Updated Date <i class="fa fa-sort pull-right" ng-show="OrderBy != 'updated_date'"></i>
                                                            <span ng-show="OrderBy == 'updated_date'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                    <!-- 18. Updated By -->
                                                    <th style="cursor:pointer;" ng-click="MakeOrderBy('updated_by')" ng-class="{'orderby' : OrderBy == 'updated_by'}">
                                                        <div style="min-width: 100px;">
                                                            Updated By <i class="fa fa-sort pull-right" ng-show="OrderBy != 'updated_by'"></i>
                                                            <span ng-show="OrderBy == 'updated_by'">
                                                                <i class="fa fa-sort-asc pull-right" ng-show="OrderByType == 'asc'"></i>
                                                                <i class="fa fa-sort-desc pull-right" ng-show="OrderByType == 'desc'"></i>
                                                            </span>
                                                        </div>
                                                    </th>

                                                </tr>

                                                <tr class="errornone">
                                                     <!-- 1. Edit -->
                                                     <td>&nbsp;</td>

                                                    <!-- 2. Priority -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="priority" ng-model="filter_text[0].priority" ng-change="MakeFilter()" aria-label="Priority Filter" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 3. Rule Name -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="rule_name" ng-model="filter_text[0].rule_name" ng-change="MakeFilter()" aria-label="Rule Name Filter" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 4. Description -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="rule_description" ng-model="filter_text[0].rule_description" ng-change="MakeFilter()" aria-label="Description Filter" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 5. Customer -->
                                                    <td>
                                                        <div style="display: flex; align-items: center; gap: 3px;">
                                                            <md-input-container class="md-block mt-0" style="flex: 1; margin: 0;">
                                                                <input type="text" name="CustomerName" ng-model="filter_text[0].CustomerName" ng-change="MakeFilter()" aria-label="Customer Filter" />
                                                            </md-input-container>
                                                            <button type="button" ng-click="filter_text[0].CustomerName='All'; MakeFilter()"
                                                                    style="background: #4CAF50; color: white; border: none; border-radius: 2px; padding: 3px 6px; font-size: 9px; cursor: pointer; height: 22px; min-width: 28px;">All</button>
                                                            <button type="button" ng-click="filter_text[0].CustomerName=''; MakeFilter()"
                                                                    style="background: #FF9800; color: white; border: none; border-radius: 2px; padding: 3px 5px; font-size: 11px; cursor: pointer; height: 22px; min-width: 22px;">×</button>
                                                        </div>
                                                    </td>

                                                    <!-- 6. Facility -->
                                                    <td>
                                                        <div style="display: flex; align-items: center; gap: 3px;">
                                                            <md-input-container class="md-block mt-0" style="flex: 1; margin: 0;">
                                                                <input type="text" name="FacilityName" ng-model="filter_text[0].FacilityName" ng-change="MakeFilter()" aria-label="Facility Filter" />
                                                            </md-input-container>
                                                            <button type="button" ng-click="filter_text[0].FacilityName='All'; MakeFilter()"
                                                                    style="background: #4CAF50; color: white; border: none; border-radius: 2px; padding: 3px 6px; font-size: 9px; cursor: pointer; height: 22px; min-width: 28px;">All</button>
                                                            <button type="button" ng-click="filter_text[0].FacilityName=''; MakeFilter()"
                                                                    style="background: #FF9800; color: white; border: none; border-radius: 2px; padding: 3px 5px; font-size: 11px; cursor: pointer; height: 22px; min-width: 22px;">×</button>
                                                        </div>
                                                    </td>

                                                    <!-- 7. Workflow -->
                                                    <td>
                                                        <div style="display: flex; align-items: center; gap: 3px;">
                                                            <md-input-container class="md-block mt-0" style="flex: 1; margin: 0;">
                                                                <input type="text" name="workflow" ng-model="filter_text[0].workflow" ng-change="MakeFilter()" aria-label="Workflow Filter" />
                                                            </md-input-container>
                                                            <button type="button" ng-click="filter_text[0].workflow='All'; MakeFilter()"
                                                                    style="background: #4CAF50; color: white; border: none; border-radius: 2px; padding: 3px 6px; font-size: 9px; cursor: pointer; height: 22px; min-width: 28px;">All</button>
                                                            <button type="button" ng-click="filter_text[0].workflow=''; MakeFilter()"
                                                                    style="background: #FF9800; color: white; border: none; border-radius: 2px; padding: 3px 5px; font-size: 11px; cursor: pointer; height: 22px; min-width: 22px;">×</button>
                                                        </div>
                                                    </td>

                                                    <!-- 8. Part Type -->
                                                    <td>
                                                        <div style="display: flex; align-items: center; gap: 3px;">
                                                            <md-input-container class="md-block mt-0" style="flex: 1; margin: 0;">
                                                                <input type="text" name="part_types" ng-model="filter_text[0].part_types" ng-change="MakeFilter()" aria-label="Part Types Filter" />
                                                            </md-input-container>
                                                            <button type="button" ng-click="filter_text[0].part_types='All'; MakeFilter()"
                                                                    style="background: #4CAF50; color: white; border: none; border-radius: 2px; padding: 3px 6px; font-size: 9px; cursor: pointer; height: 22px; min-width: 28px;">All</button>
                                                            <button type="button" ng-click="filter_text[0].part_types=''; MakeFilter()"
                                                                    style="background: #FF9800; color: white; border: none; border-radius: 2px; padding: 3px 5px; font-size: 11px; cursor: pointer; height: 22px; min-width: 22px;">×</button>
                                                        </div>
                                                    </td>

                                                    <!-- 9. Rule Summary -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="rule_summary" ng-model="filter_text[0].rule_summary" ng-change="MakeFilter()" aria-label="Rule Summary Filter" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 10. Disposition -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="disposition" ng-model="filter_text[0].disposition" ng-change="MakeFilter()" aria-label="Disposition Filter" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 11. Rule ID -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="rule_id_text" ng-model="filter_text[0].rule_id_text" ng-change="MakeFilter()" aria-label="Rule ID Filter" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 12. Source Type -->
                                                    <td>
                                                        <div style="display: flex; align-items: center; gap: 3px;">
                                                            <md-input-container class="md-block mt-0" style="flex: 1; margin: 0;">
                                                                <input type="text" name="SourceTypeName" ng-model="filter_text[0].SourceTypeName" ng-change="MakeFilter()" aria-label="Source Type Filter" />
                                                            </md-input-container>
                                                            <button type="button" ng-click="filter_text[0].SourceTypeName='All'; MakeFilter()"
                                                                    style="background: #4CAF50; color: white; border: none; border-radius: 2px; padding: 3px 6px; font-size: 9px; cursor: pointer; height: 22px; min-width: 28px;">All</button>
                                                            <button type="button" ng-click="filter_text[0].SourceTypeName=''; MakeFilter()"
                                                                    style="background: #FF9800; color: white; border: none; border-radius: 2px; padding: 3px 5px; font-size: 11px; cursor: pointer; height: 22px; min-width: 22px;">×</button>
                                                        </div>
                                                    </td>

                                                    <!-- 13. Material Type -->
                                                    <td>
                                                        <div style="display: flex; align-items: center; gap: 3px;">
                                                            <md-input-container class="md-block mt-0" style="flex: 1; margin: 0;">
                                                                <input type="text" name="MaterialType" ng-model="filter_text[0].MaterialType" ng-change="MakeFilter()" aria-label="Material Type Filter" />
                                                            </md-input-container>
                                                            <button type="button" ng-click="filter_text[0].MaterialType='All'; MakeFilter()"
                                                                    style="background: #4CAF50; color: white; border: none; border-radius: 2px; padding: 3px 6px; font-size: 9px; cursor: pointer; height: 22px; min-width: 28px;">All</button>
                                                            <button type="button" ng-click="filter_text[0].MaterialType=''; MakeFilter()"
                                                                    style="background: #FF9800; color: white; border: none; border-radius: 2px; padding: 3px 5px; font-size: 11px; cursor: pointer; height: 22px; min-width: 22px;">×</button>
                                                        </div>
                                                    </td>

                                                    <!-- 14. Status -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="status" ng-model="filter_text[0].status" ng-change="MakeFilter()" aria-label="Status Filter" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 15. Created Date -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="created_date" ng-model="filter_text[0].created_date" ng-change="MakeFilter()" aria-label="Created Date Filter" placeholder="YYYY-MM-DD" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 16. Created By -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="created_by" ng-model="filter_text[0].created_by" ng-change="MakeFilter()" aria-label="Created By Filter" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 17. Updated Date -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="updated_date" ng-model="filter_text[0].updated_date" ng-change="MakeFilter()" aria-label="Updated Date Filter" placeholder="YYYY-MM-DD" />
                                                        </md-input-container>
                                                    </td>

                                                    <!-- 18. Updated By -->
                                                    <td>
                                                        <md-input-container class="md-block mt-0">
                                                            <input type="text" name="updated_by" ng-model="filter_text[0].updated_by" ng-change="MakeFilter()" aria-label="Updated By Filter" />
                                                        </md-input-container>
                                                    </td>
                                                </tr>
                                            </thead>

                                            <tbody ng-show="pagedItems.length > 0">
                                                <tr ng-repeat="product in pagedItems">
                                                    <!-- 1. Edit -->
                                                    <td>
                                                        <a href="#!/BusinessRule/{{product.rule_id}}"><md-icon class="material-icons text-danger" ng-show="product.VersionStatus == 'Pending'">edit</md-icon></a>
                                                    </td>

                                                    <!-- 2. Priority -->
                                                    <td>
                                                        {{product.priority}}
                                                    </td>

                                                    <!-- 3. Rule Name -->
                                                    <td>
                                                        {{product.rule_name}}
                                                    </td>

                                                    <!-- 4. Description -->
                                                    <td>
                                                        {{product.rule_description}}
                                                    </td>

                                                    <!-- 5. Customer -->
                                                    <td>
                                                        <span ng-if="product.AWSCustomerID == 'all' || product.AWSCustomerID == 'All'">All</span>
                                                        <span ng-if="product.AWSCustomerID != 'all' && product.AWSCustomerID != 'All'">{{product.CustomerName || product.AWSCustomerID}}</span>
                                                    </td>

                                                    <!-- 6. Facility -->
                                                    <td>
                                                        <span ng-if="product.FacilityID == 'all' || product.FacilityID == 'All'">All</span>
                                                        <span ng-if="product.FacilityID != 'all' && product.FacilityID != 'All'">{{product.FacilityName}}</span>
                                                    </td>

                                                    <!-- 7. Workflow -->
                                                    <td>
                                                        <span ng-if="product.workflow_id == 'all' || product.workflow_id == 'All'">All</span>
                                                        <span ng-if="product.workflow_id != 'all' && product.workflow_id != 'All'">{{product.workflow}}</span>
                                                    </td>

                                                    <!-- 8. Part Type -->
                                                    <td>
                                                        {{product.part_types}}
                                                    </td>

                                                    <!-- 9. Rule Summary -->
                                                    <td>
                                                        {{product.rule_summary}}
                                                    </td>

                                                    <!-- 10. Disposition -->
                                                    <td>
                                                        {{product.disposition}}
                                                    </td>

                                                    <!-- 11. Rule ID -->
                                                    <td>
                                                        {{product.rule_id_text}}
                                                    </td>

                                                    <!-- 12. Source Type -->
                                                    <td>
                                                        <span ng-if="product.idCustomertype == 'all' || product.idCustomertype == 'All'">All</span>
                                                        <span ng-if="product.idCustomertype != 'all' && product.idCustomertype != 'All'">{{product.SourceTypeName}}</span>
                                                    </td>

                                                    <!-- 13. Material Type -->
                                                    <td>                                                        
                                                        <span ng-if="product.MaterialType == 'all'">All</span>
                                                        <span ng-if="product.MaterialType != 'all'">{{product.MaterialType}}</span>
                                                    </td>

                                                    <!-- 14. Status -->
                                                    <td>
                                                        <md-switch ng-model="product.status" aria-label="default" class="md-default" ng-true-value="'Active'" ng-false-value="'Inactive'" ng-change="ChangeRuleStatus(product,$event)" ng-show="!product.loading">{{product.status}}</md-switch>
                                                        <i class="fa fa-spinner" style="font-size: 16px;" aria-hidden="true" ng-show="product.loading"></i>
                                                    </td>

                                                    <!-- 15. Created Date -->
                                                    <td>
                                                        <span>{{product.created_date | date:'MM/dd/yyyy HH:mm'}}</span>
                                                    </td>

                                                    <!-- 16. Created By -->
                                                    <td>
                                                        <span>{{product.created_by}}</span>
                                                    </td>

                                                    <!-- 17. Updated Date -->
                                                    <td>
                                                        <span>{{product.updated_date | date:'MM/dd/yyyy HH:mm'}}</span>
                                                    </td>

                                                    <!-- 18. Updated By -->
                                                    <td>
                                                        <span>{{product.updated_by}}</span>
                                                    </td>

                                                </tr>
                                            </tbody>

                                            <tfoot>
                                                <tr>
                                                    <td colspan="18">
                                                        <div>
                                                            <ul class="pagination">
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="firstPage()"><< First</a>
                                                                </li>
                                                                <li ng-class="prevPageDisabled()">
                                                                    <a href ng-click="prevPage()"><< Prev</a>
                                                                </li>
                                                                <li ng-repeat="n in range()" ng-class="{active: n == currentPage}" ng-click="setPage(n)" ng-show="n >= 0">
                                                                    <a style="cursor:pointer;">{{n+1}}</a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="nextPage()">Next >></a>
                                                                </li>
                                                                <li ng-class="nextPageDisabled()">
                                                                    <a href ng-click="lastPage()">Last >></a>
                                                                </li>
                                                            </ul>
                                                        </div>
                                                    </td>
                                                </tr>
                                            </tfoot>

                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </md-card>

                </div>

            </article>
        </div>
    </div>

    <script>
        $(document).ready(function() {
            $("#left-button").css("display", "none");
        });
    </script>

    <script>

        $("#right-button").click(function() {
        event.preventDefault();
        $(".table-responsive").animate(
            {
            scrollLeft: "+=300px"
            },
            "slow"
        );
        });

        $("#left-button").click(function() {
        event.preventDefault();
        $(".table-responsive").animate(
            {
            scrollLeft: "-=300px"
            },
            "slow"
        );
        });


        let rightBtn = $('#right-button');
        let leftBtn = $('#left-button');

        $('.table-container').on('scroll', function (e) {
            let el = e.currentTarget;
            let sl = el.scrollLeft;
            let cw = el.clientWidth;
            let sw = el.scrollWidth;

            let showRightBtn = sw !== sl + cw;
            let showLeftBtn = sl !== 0;

            showRightBtn ? rightBtn.fadeIn() : rightBtn.fadeOut();
            showLeftBtn ? leftBtn.fadeIn() : leftBtn.fadeOut();
        });


    </script>

</div>

<!-- Export Version Selection Dialog -->
<div ng-controller="BusinessRuleListController">
    <script type="text/ng-template" id="exportDialog.html">
        <md-dialog aria-label="Export Business Rules" style="min-width: 500px;">
            <form name="exportForm">
                <md-toolbar>
                    <div class="md-toolbar-tools">
                        <h2>Export Business Rules</h2>
                        <span flex></span>
                        <md-button class="md-icon-button" ng-click="cancel()">
                            <md-icon class="material-icons">close</md-icon>
                        </md-button>
                    </div>
                </md-toolbar>

                <md-dialog-content style="padding: 20px;">
                    <div class="md-dialog-content">
                        <h3>Select Versions to Export:</h3>
                        <div style="max-height: 300px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; margin: 10px 0;">
                            <md-checkbox ng-model="selectAllVersions"
                                        ng-change="toggleAllVersions()"
                                        style="margin-bottom: 10px;">
                                <strong>Select All Versions</strong>
                            </md-checkbox>
                            <div ng-repeat="version in RuleVersions" style="margin-left: 20px;">
                                <md-checkbox ng-model="version.selected"
                                            ng-change="updateSelectAll()"
                                            style="margin-bottom: 5px;">
                                    {{version.version_name}}
                                    <span style="color: #4CAF50; font-weight: bold;" ng-show="version.current_version == '1'"> (Current Version)</span>
                                    <span style="color: #FF9800; font-weight: bold;" ng-show="version.previous_version == '1'"> (Previous Version)</span>
                                </md-checkbox>
                            </div>
                        </div>
                        <div ng-show="getSelectedVersionsCount() == 0" style="color: red; margin-top: 10px;">
                            Please select at least one version to export.
                        </div>
                    </div>
                </md-dialog-content>

                <md-dialog-actions layout="row">
                    <span flex></span>
                    <md-button ng-click="cancel()" class="md-primary">
                        Cancel
                    </md-button>
                    <md-button ng-click="exportSelectedVersions()"
                              ng-disabled="getSelectedVersionsCount() == 0"
                              class="md-primary md-raised">
                        Export ({{getSelectedVersionsCount()}} version{{getSelectedVersionsCount() != 1 ? 's' : ''}})
                    </md-button>
                </md-dialog-actions>
            </form>
        </md-dialog>
    </script>
</div>