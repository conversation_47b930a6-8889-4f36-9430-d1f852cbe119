(function () {
    'use strict';

    var module = angular.module('app', [
        // Core modules
         'app.core'

        // Custom Feature modules
        ,'app.ui'
        ,'app.ui.form'
        ,'app.ui.form.validation'


        // 3rd party feature modules
        ,'md.data.table'
        ,'global'
        ,'angularFileUpload'
        ,'md.data.table'
    ]);

    module.config(['$stateProvider', '$urlRouterProvider', '$ocLazyLoadProvider',
            function($stateProvider, $urlRouterProvider, $ocLazyLoadProvider) {

            $stateProvider
                .state('BusinessRule', {
                    url: '/BusinessRule',
                    templateUrl: "templates/BusinessRule.html"
                })
                .state('BusinessRule/:rule_id', {
                    url: '/BusinessRule/:rule_id',
                    templateUrl: "templates/BusinessRule.html"
                })
                .state('BusinessRuleList', {
                    url: '/BusinessRuleList',
                    templateUrl: "templates/BusinessRuleList.html"
                })
                .state('BusinessRuleList/:version_id', {
                    url: '/BusinessRuleList/:version_id',
                    templateUrl: "templates/BusinessRuleList.html"
                })
            $urlRouterProvider
                .when('/', '/BusinessRuleList')
                .otherwise('/BusinessRuleList');
        }
    ]);



    module.controller("BusinessRule", function ($scope,$http,$rootScope,$mdToast,$mdDialog,$stateParams,facilityinformation) {
        $scope.business_rule = {
            'conditions': [],
            'input_type': [],
            'input_id': [],          // Initialize as array for multi-select
            'AWSCustomerID': 'all',  // Initialize with 'all' for single-select
            'workflow_id': 'all',    // Initialize with 'all' for single-select
            'FacilityID': 'all',     // Initialize with 'all' for single-select
            'part_types': ['All'],   // Initialize with 'All' for multi-select
            'idCustomertype': 'all', // Initialize with 'all' for Source Type
            'MaterialType': 'all',   // Initialize with 'all' for Material Type
            'isGlobalRule': false    // Initialize Global Rule checkbox as unchecked
        };

        // Function to handle Global Rule checkbox
        $scope.setGlobalRule = function() {
            if ($scope.business_rule.isGlobalRule) {
                // Set all fields to "All" when Global Rule is checked
                $scope.business_rule.AWSCustomerID = 'all';
                $scope.business_rule.workflow_id = 'all';
                $scope.business_rule.FacilityID = 'all';
                $scope.business_rule.part_types = ['All'];
                $scope.business_rule.idCustomertype = 'all';
                $scope.business_rule.MaterialType = 'all';

                // Clear input_type and input_id to avoid validation issues
                $scope.business_rule.input_type = [];
                $scope.business_rule.input_id = [];

                // Reload Input Results for all workflows when Global Rule is selected
                $scope.GetInputResults();

                // Force Angular to update the model
                if (!$scope.$$phase) {
                    $scope.$apply();
                }
            }
        };

        // Function to handle "All" selection in Part Type dropdown
        $scope.selectAllPartType = function(event) {
            // Prevent the default behavior to ensure our custom logic works
            if (event) {
                event.stopPropagation();
            }

            // When "All" is clicked, make it the only selection
            $scope.business_rule.part_types = ['All'];

            // Force Angular to update the model
            if (!$scope.$$phase) {
                $scope.$apply();
            }
        };

        // Function to handle Part Type selection changes
        $scope.handlePartTypeSelection = function() {
            // Check if part_types exists and has items
            if ($scope.business_rule.part_types && $scope.business_rule.part_types.length > 0) {
                // Find if "All" is in the selection
                var allIndex = $scope.business_rule.part_types.indexOf('All');

                // If "All" is in the selection
                if (allIndex !== -1) {
                    // If "All" is selected along with other options
                    if ($scope.business_rule.part_types.length > 1) {
                        // If "All" was just added (it's the last item in the array)
                        if ($scope.business_rule.part_types[$scope.business_rule.part_types.length - 1] === 'All') {
                            // Keep only "All" selected
                            setTimeout(function() {
                                $scope.business_rule.part_types = ['All'];
                                if (!$scope.$$phase) {
                                    $scope.$apply();
                                }
                            }, 0);
                        } else {
                            // If other options were added while "All" was selected, remove "All"
                            $scope.business_rule.part_types.splice(allIndex, 1);
                        }
                    }
                    // If only "All" is selected, keep it that way
                } else {
                    // "All" is not in the selection, make sure it stays unselected
                    // (No action needed, as "All" is already not in the selection)
                }
            }
        };

        // Function to handle Customer ID selection changes
        $scope.handleCustomerSelection = function() {
            // Check if AWSCustomerID exists and has items
            if ($scope.business_rule.AWSCustomerID && $scope.business_rule.AWSCustomerID.length > 0) {
                // Find if "All" is in the selection
                var allIndex = $scope.business_rule.AWSCustomerID.indexOf('All');

                // If "All" is in the selection
                if (allIndex !== -1) {
                    // If "All" is selected along with other options
                    if ($scope.business_rule.AWSCustomerID.length > 1) {
                        // If "All" was just added (it's the last item in the array)
                        if ($scope.business_rule.AWSCustomerID[$scope.business_rule.AWSCustomerID.length - 1] === 'All') {
                            // "All" was just selected, so keep only "All"
                            setTimeout(function() {
                                $scope.business_rule.AWSCustomerID = ['All'];
                                if (!$scope.$$phase) {
                                    $scope.$apply();
                                }
                            }, 0);
                        } else {
                            // Other customers were selected after "All" was already selected
                            // Remove "All" to allow the new customer selections
                            setTimeout(function() {
                                $scope.business_rule.AWSCustomerID.splice(allIndex, 1);
                                if (!$scope.$$phase) {
                                    $scope.$apply();
                                }
                            }, 0);
                        }
                    }
                    // If only "All" is selected, keep it that way
                } else {
                    // "All" is not in the selection, allow normal multi-select behavior
                    // (No action needed)
                }
            }
        };
        $scope.condition = {};
        $scope.Dispositions = [];
        $scope.WorkFlows = [];
        $scope.InputResults = [];
        $scope.Attributes = [];
        $scope.AttributeValues = [];
        $scope.AWSCustomers = [];
        $scope.PartTypes = [];
        $scope.SourceTypes = [];
        $scope.MaterialTypes = [];
        $scope.newcondition = {
            'mpn_input': ''
        };
        $rootScope.$broadcast('preloader:active');

        facilityinformation.async().then(function(d) { //2. so you can use .then()
            $scope.Facility = d['data']['Result'];
        });

        // Load Source Types when page loads


        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'businessrules/includes/businessrule_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetSourceTypes',
            success: function(data){
                if(data.Success) {
                    // Add "All" option at the beginning of the array
                    //var sourceTypes = [{ idCustomertype: 'all', Cumstomertype: 'All', Description: 'All source types' }];
                    // Concatenate the rest of the source types
                    //$scope.SourceTypes = sourceTypes.concat(data.Result);
                    $scope.SourceTypes = data.Result;
                } else {
                    $scope.SourceTypes = [{ idCustomertype: 'all', Cumstomertype: 'All', Description: 'All source types' }];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });

        // Load Material Types when page loads
        jQuery.ajax({
            url: host+'businessrules/includes/businessrule_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetMaterialTypes',
            success: function(data){
                if(data.Success) {
                    // Add "All" option at the beginning of the array
                    //var materialTypes = [{ MaterialType: 'All', value: 'all' }];
                    // Concatenate the rest of the material types
                    //$scope.MaterialTypes = materialTypes.concat(data.Result);
                    $scope.MaterialTypes = data.Result;
                } else {
                    $scope.MaterialTypes = [{ MaterialType: 'All', value: 'all' }];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });

        // Load Part Types when page loads (all part types from all facilities)
        jQuery.ajax({
            url: host+'businessrules/includes/businessrule_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAllPartTypes',
            success: function(data){
                if(data.Success) {
                    // Add "All" option at the beginning of the array
                    var partTypes = [{ PartTypeName: 'All' }];
                    // Concatenate the rest of the part types
                    $scope.PartTypes = partTypes.concat(data.Result);
                } else {
                    $scope.PartTypes = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });

        // Load all input results when page loads
        jQuery.ajax({
            url: host+'businessrules/includes/businessrule_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetInputResults&workflow_id=all',
            success: function(data){
                if(data.Success) {
                    // Group InputResults by input_type
                    $scope.InputResults = data.Result;
                    $scope.groupedInputResults = {};
                    $scope.inputTypeGroups = [];

                    // Group by input_type
                    angular.forEach($scope.InputResults, function(item) {
                        if (!$scope.groupedInputResults[item.input_type]) {
                            $scope.groupedInputResults[item.input_type] = [];
                            $scope.inputTypeGroups.push(item.input_type);
                        }
                        $scope.groupedInputResults[item.input_type].push(item);
                    });

                    // Sort input types alphabetically
                    $scope.inputTypeGroups.sort();

                    // Sort inputs within each group alphabetically
                    angular.forEach($scope.inputTypeGroups, function(inputType) {
                        $scope.groupedInputResults[inputType].sort(function(a, b) {
                            return a.input.localeCompare(b.input);
                        });
                    });

                    // Initialize selection state for each group
                    $scope.inputTypeSelections = {};
                    angular.forEach($scope.inputTypeGroups, function(inputType) {
                        $scope.inputTypeSelections[inputType] = false;
                    });
                } else {
                    $scope.InputResults = [];
                    $scope.groupedInputResults = {};
                    $scope.inputTypeGroups = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });


        // Fetch AWS Customers for dropdown
        jQuery.ajax({
            url: host + 'businessrules/includes/businessrule_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetAWSCustomers',
            success: function (data) {
                if (data.Success == true) {
                    // Add "All" option at the beginning of the array
                    var customers = [{ AWSCustomerID: 'All', Customer: 'All' }];
                    // Concatenate the rest of the customers
                    $scope.AWSCustomers = customers.concat(data.Result);
                } else {
                    $scope.AWSCustomers = [{ AWSCustomerID: 'All', Customer: 'All' }];
                }
                initSessionTime(); $scope.$apply();
            }, error: function (data) {
                $scope.data = data;
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
            url: host+'businessrules/includes/businessrule_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetDispositions',
            success: function(data){
                if(data.Success) {
                    $scope.Dispositions = data.Result;
                } else {
                    $scope.Dispositions = [];
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
            url: host+'businessrules/includes/businessrule_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetBusinessRuleAttributes',
            success: function(data){
                if(data.Success) {
                    $scope.Attributes = data.Result;
                } else {
                    $scope.Attributes = [];
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                initSessionTime(); $scope.$apply();
            }
        });

        $scope.RuleVersions = [];
        jQuery.ajax({
            url: host+'businessrules/includes/businessrule_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetPendingBRVersions',
            success: function(data) {
                if(data.Success) {
                    $scope.RuleVersions = data.Result;
                } else {
                    $mdToast.show (
                        $mdToast.simple()
                            .content(data.Result)
                            .action('OK')
                            .position('right')
                            .hideDelay(0)
                            .toastClass('md-toast-danger md-block')
                    );
                }
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                alert(data.Result);
                $scope.error = data;
                initSessionTime(); $scope.$apply();
            }
        });

        jQuery.ajax({
            url: host+'businessrules/includes/businessrule_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetWorkflows',
            success: function(data){
                if(data.Success) {
                    $scope.WorkFlows = data.Result;
                } else {
                    $scope.WorkFlows = [];
                }
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });


        if($stateParams.rule_id) {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'businessrules/includes/businessrule_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetBusinessRuleDetails&rule_id='+$stateParams.rule_id,
                success: function(data){
                    if(data.Success) {
                        $scope.business_rule = data.Result;

                        // Format user names for audit fields
                        if($scope.business_rule.CreatedByFirstName && $scope.business_rule.CreatedByLastName) {
                            $scope.business_rule.created_by = $scope.business_rule.CreatedByFirstName + ' ' + $scope.business_rule.CreatedByLastName;
                        }

                        if($scope.business_rule.UpdatedByFirstName && $scope.business_rule.UpdatedByLastName) {
                            $scope.business_rule.updated_by = $scope.business_rule.UpdatedByFirstName + ' ' + $scope.business_rule.UpdatedByLastName;
                        }

                        $scope.GetInputResults1();
                        if($scope.business_rule.sub_disposition_id > 0) {
                            $scope.business_rule.business_SubDispositionExists = true;
                            $scope.GetSubDispositions1();
                        }
                    } else {
                        $scope.business_rule = {'conditions': [],'input_type': '','input_id': ''};
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }
            });
        }

        $scope.AddCondition = function () {
            for(var i=0;i<$scope.Attributes.length;i++) {
                if($scope.Attributes[i]['attribute_id'] == $scope.newcondition.attribute_id) {
                    if($scope.newcondition.attribute_id != 25 && $scope.newcondition.attribute_id != 29) {
                        var value = '';
                        for(var j=0;j<$scope.newcondition.value.length;j++) {
                            if(j > 0) {
                                value = value + ' OR ';
                            }
                            if($scope.newcondition.attribute_id == '15') { //If Attribute is Manufacturer
                                for(var k=0;k<$scope.AttributeValues.length;k++) {
                                    if($scope.AttributeValues[k]['idManufacturer'] == $scope.newcondition.value[j]) {
                                        value = value + $scope.AttributeValues[k]['ManufacturerName'];
                                    }
                                }
                                //value = value + $scope.newcondition.value[j];
                            } else if($scope.newcondition.attribute_id == '39') {//If Attribute is COO
                                for(var k=0;k<$scope.AttributeValues.length;k++) {
                                    if($scope.AttributeValues[k]['COOID'] == $scope.newcondition.value[j]) {
                                        value = value + $scope.AttributeValues[k]['COO'];
                                    }
                                }

                            } else {
                                value = value + $scope.newcondition.value[j];
                            }
                        }
                        //$scope.business_rule.conditions.push({'attribute_id':$scope.Attributes[i]['attribute_id'],'attribute_name':$scope.Attributes[i]['attribute_name'],'operator':$scope.newcondition.operator,'value':$scope.newcondition.value});
                        $scope.business_rule.conditions.push({'attribute_id':$scope.Attributes[i]['attribute_id'],'attribute_name':$scope.Attributes[i]['attribute_name'],'operator':$scope.newcondition.operator,'value':value,'value_array':$scope.newcondition.value});
                        $scope.newcondition.value = '';
                        $scope.newcondition.operator = '';
                        $scope.newcondition.attribute_id = '';
                    } else if($scope.newcondition.attribute_id == 25) { // For MPN - multiple values
                        // Parse comma-separated MPNs
                        var mpnValues = [];
                        var displayValue = '';

                        if($scope.newcondition.mpn_input && $scope.newcondition.mpn_input.trim()) {
                            var mpnArray = $scope.newcondition.mpn_input.split(',');
                            for(var m = 0; m < mpnArray.length; m++) {
                                var trimmedMpn = mpnArray[m].trim();
                                if(trimmedMpn) {
                                    mpnValues.push(trimmedMpn);
                                    if(m > 0) {
                                        displayValue += ' OR ';
                                    }
                                    displayValue += trimmedMpn;
                                }
                            }
                        }

                        if(mpnValues.length > 0) {
                            $scope.business_rule.conditions.push({
                                'attribute_id': $scope.Attributes[i]['attribute_id'],
                                'attribute_name': $scope.Attributes[i]['attribute_name'],
                                'operator': $scope.newcondition.operator,
                                'value': displayValue,
                                'value_array': mpnValues
                            });
                            $scope.newcondition.mpn_input = '';
                            $scope.newcondition.operator = '';
                            $scope.newcondition.attribute_id = '';
                        }
                    } else if($scope.newcondition.attribute_id == 29) { // For other text fields
                        $scope.business_rule.conditions.push({'attribute_id':$scope.Attributes[i]['attribute_id'],'attribute_name':$scope.Attributes[i]['attribute_name'],'operator':$scope.newcondition.operator,'value':$scope.newcondition.value,'value_array':[$scope.newcondition.value]});
                        $scope.newcondition.value = '';
                        $scope.newcondition.operator = '';
                        $scope.newcondition.attribute_id = '';
                    }
                }
            }
        }

        $scope.RemoveCondition = function (ind,ev) {

            var confirm = $mdDialog.confirm()
            .title('Confirmation')
            .content('Are you sure you want to delete this condition?')
            .ariaLabel('Lucky day')
            .targetEvent(ev)
            .ok('Delete')
            .cancel('No, leave it');
            $mdDialog.show(confirm).then(function() {
                $scope.business_rule.conditions.splice(ind,1);
            }, function() {
                //$scope.status = 'You decided to keep your debt.';
            });
        }

        // Function to toggle selection of all items in a group
        $scope.toggleGroupSelection = function(inputType) {
            // Toggle the selection state for this group
            $scope.inputTypeSelections[inputType] = !$scope.inputTypeSelections[inputType];

            // Get all input_ids for this input_type
            var inputIds = [];
            angular.forEach($scope.groupedInputResults[inputType], function(item) {
                inputIds.push(item.input_id);
            });

            if ($scope.inputTypeSelections[inputType]) {
                // Add all items from this group to the selection
                angular.forEach(inputIds, function(id) {
                    if ($scope.business_rule.input_id.indexOf(id) === -1) {
                        $scope.business_rule.input_id.push(id);
                    }
                });
            } else {
                // Remove all items from this group from the selection
                $scope.business_rule.input_id = $scope.business_rule.input_id.filter(function(id) {
                    return inputIds.indexOf(id) === -1;
                });
            }

            // Force Angular to validate the form
            setTimeout(function() {
                // Mark the field as dirty to trigger validation
                var inputIdField = document.querySelector('[name="input_id"]');
                if (inputIdField) {
                    angular.element(inputIdField).controller('ngModel').$setDirty();
                    angular.element(inputIdField).controller('ngModel').$validate();
                }

                if (!$scope.$$phase) {
                    $scope.$apply();
                }
            }, 0);
        };

        // Function to check if all items in a group are selected
        $scope.isGroupSelected = function(inputType) {
            if (!$scope.groupedInputResults[inputType]) return false;

            var allSelected = true;
            angular.forEach($scope.groupedInputResults[inputType], function(item) {
                if ($scope.business_rule.input_id.indexOf(item.input_id) === -1) {
                    allSelected = false;
                }
            });

            // Update the selection state for this group
            $scope.inputTypeSelections[inputType] = allSelected;

            return allSelected;
        };

        // Function to check if any item in a group is selected
        $scope.isAnyInGroupSelected = function(inputType) {
            if (!$scope.groupedInputResults[inputType]) return false;

            var anySelected = false;
            angular.forEach($scope.groupedInputResults[inputType], function(item) {
                if ($scope.business_rule.input_id.indexOf(item.input_id) !== -1) {
                    anySelected = true;
                }
            });

            return anySelected;
        };

        // Function to update group selection state when individual items are selected/deselected
        $scope.updateGroupSelection = function() {
            angular.forEach($scope.inputTypeGroups, function(inputType) {
                $scope.isGroupSelected(inputType);
            });
        };

        $scope.GetInputResults = function () {
            // Always load input results, regardless of workflow selection
            $rootScope.$broadcast('preloader:active');

            // Always include the workflow_id parameter, even when it's 'all'
            var requestData = 'ajax=GetInputResults&workflow_id=' + $scope.business_rule.workflow_id;

            jQuery.ajax({
                url: host+'businessrules/includes/businessrule_submit.php',
                dataType: 'json',
                type: 'post',
                data: requestData,
                success: function(data){
                    if(data.Success) {
                        // Group InputResults by input_type
                        $scope.InputResults = data.Result;
                        $scope.groupedInputResults = {};
                        $scope.inputTypeGroups = [];
                        $scope.business_rule.input_id = [];

                        // Group by input_type
                        angular.forEach($scope.InputResults, function(item) {
                            if (!$scope.groupedInputResults[item.input_type]) {
                                $scope.groupedInputResults[item.input_type] = [];
                                $scope.inputTypeGroups.push(item.input_type);
                            }
                            $scope.groupedInputResults[item.input_type].push(item);
                        });

                        // Sort input types alphabetically
                        $scope.inputTypeGroups.sort();

                        // Sort inputs within each group alphabetically
                        angular.forEach($scope.inputTypeGroups, function(inputType) {
                            $scope.groupedInputResults[inputType].sort(function(a, b) {
                                return a.input.localeCompare(b.input);
                            });
                        });

                        // Initialize selection state for each group
                        $scope.inputTypeSelections = {};
                        angular.forEach($scope.inputTypeGroups, function(inputType) {
                            $scope.inputTypeSelections[inputType] = false;
                        });
                    } else {
                        $scope.InputResults = [];
                        $scope.groupedInputResults = {};
                        $scope.inputTypeGroups = [];
                        $scope.business_rule.input_id = [];
                    }
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.GetInputResults1 = function () {
            // Always load input results, regardless of workflow selection
            $rootScope.$broadcast('preloader:active');

            // Always include the workflow_id parameter, even when it's 'all'
            var requestData = 'ajax=GetInputResults&workflow_id=' + $scope.business_rule.workflow_id;

            jQuery.ajax({
                url: host+'businessrules/includes/businessrule_submit.php',
                dataType: 'json',
                type: 'post',
                data: requestData,
                success: function(data){
                    if(data.Success) {
                        // Group InputResults by input_type
                        $scope.InputResults = data.Result;
                        $scope.groupedInputResults = {};
                        $scope.inputTypeGroups = [];

                        // Group by input_type
                        angular.forEach($scope.InputResults, function(item) {
                            if (!$scope.groupedInputResults[item.input_type]) {
                                $scope.groupedInputResults[item.input_type] = [];
                                $scope.inputTypeGroups.push(item.input_type);
                            }
                            $scope.groupedInputResults[item.input_type].push(item);
                        });

                        // Sort input types alphabetically
                        $scope.inputTypeGroups.sort();

                        // Sort inputs within each group alphabetically
                        angular.forEach($scope.inputTypeGroups, function(inputType) {
                            $scope.groupedInputResults[inputType].sort(function(a, b) {
                                return a.input.localeCompare(b.input);
                            });
                        });

                        // Initialize selection state for each group
                        $scope.inputTypeSelections = {};
                        angular.forEach($scope.inputTypeGroups, function(inputType) {
                            $scope.inputTypeSelections[inputType] = false;
                        });

                        // Update group selection states based on current selections
                        $scope.updateGroupSelection();
                    } else {
                        $scope.InputResults = [];
                        $scope.groupedInputResults = {};
                        $scope.inputTypeGroups = [];
                    }
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    initSessionTime(); $scope.$apply();
                }
            });
        };

        $scope.GetAttributeValues = function () {
            $scope.newcondition.operator = '';

            // Clear previous values
            $scope.newcondition.value = null;
            $scope.newcondition.mpn_input = '';

            if($scope.newcondition.attribute_id > 0 && $scope.newcondition.attribute_id != 25 && $scope.newcondition.attribute_id != 29) { //Not for MPN
                $rootScope.$broadcast('preloader:active');

                jQuery.ajax({
                    url: host+'businessrules/includes/businessrule_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetAttributeValues&attribute_id='+$scope.newcondition.attribute_id,
                    success: function(data){
                        if(data.Success) {
                            $scope.AttributeValues = data.Result;
                            $scope.newcondition.value = null;
                        } else {
                            $scope.AttributeValues = [];
                            $scope.newcondition.value = null;
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });

            } else {
                $scope.AttributeValues = [];
                $scope.newcondition.value = null;
            }
        };

        $scope.GetPartTypes = function () {
            if($scope.business_rule.FacilityID) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'businessrules/includes/businessrule_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetPartTypes&FacilityID='+$scope.business_rule.FacilityID,
                    success: function(data){
                        if(data.Success) {
                            // Add "All" option at the beginning of the array
                            var partTypes = [{ PartTypeName: 'All' }];
                            // Concatenate the rest of the part types
                            $scope.PartTypes = partTypes.concat(data.Result);
                        } else {
                            $scope.PartTypes = [{ PartTypeName: 'All' }];
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            } else {
                $scope.PartTypes = [{ PartTypeName: 'All' }];
            }
        };

        $scope.CreateBusinessRule = function () {
            if($scope.business_rule.input_id == null) {
                $scope.business_rule.input_id = [];
            }

            // Check if input_type is defined and has values
            var hasInputType = $scope.business_rule.input_type && $scope.business_rule.input_type.length > 0;

            // Check if input_id is defined and has values
            var hasInputId = $scope.business_rule.input_id && $scope.business_rule.input_id.length > 0;

            // Validate that either input_type OR input_id is selected, but not both and not neither
            if((hasInputType && hasInputId) || (!hasInputType && !hasInputId)) {
                $mdToast.show(
                    $mdToast.simple()
                        .content('Select either Input Type or Input Results, but not both')
                        .position('right')
                        .hideDelay(3000)
                );
            } else {
                $scope.business_rule.busy = true;
                //if($scope.business_rule.conditions.length == 0) {
                if(false) {
                    $mdToast.show(
                        $mdToast.simple()
                            .content('No Conditions are added')
                            .position('right')
                            .hideDelay(3000)
                    );
                    $scope.business_rule.busy = false;
                } else {

                    $rootScope.$broadcast('preloader:active');
                    if($scope.business_rule.input_type != '') {
                        //Delete existing input-type condition
                        if($scope.business_rule.conditions.length > 0) {
                            for(var k=0;k<$scope.business_rule.conditions.length;k++) {
                                if($scope.business_rule.conditions[k].attribute_id == '14') {
                                    $scope.business_rule.conditions.splice(k,1);
                                }
                            }
                        }

                        var value = '';
                        // for(var j=0;j<$scope.business_rule.input_type.length;j++) {
                        //     if(j > 0) {
                        //         value = value + ' OR ';
                        //     }
                        //     value = value + $scope.business_rule.input_type[j];
                        // }
                        // $scope.business_rule.conditions.unshift({'attribute_id':'14','attribute_name':'Input-Type','operator':'==','value':value,'value_array':$scope.business_rule.input_type});

                    }

                    // Make sure AWSCustomerID is included in the data sent to the server
                    if (!$scope.business_rule.AWSCustomerID) {
                        $scope.business_rule.AWSCustomerID = 'all';
                    }

                    // Make sure workflow_id is included in the data sent to the server
                    if (!$scope.business_rule.workflow_id) {
                        $scope.business_rule.workflow_id = 'all';
                    }

                    // Make sure FacilityID is included in the data sent to the server
                    if (!$scope.business_rule.FacilityID) {
                        $scope.business_rule.FacilityID = 'all';
                    }

                    // Make sure part_types is included in the data sent to the server
                    if (!$scope.business_rule.part_types) {
                        $scope.business_rule.part_types = [];
                    }

                    // Make sure idCustomertype is included in the data sent to the server
                    if (!$scope.business_rule.idCustomertype) {
                        $scope.business_rule.idCustomertype = 'all';
                    }

                    // Make sure MaterialType is included in the data sent to the server
                    if (!$scope.business_rule.MaterialType) {
                        $scope.business_rule.MaterialType = 'all';
                    }

                    jQuery.ajax({
                        url: host+'businessrules/includes/businessrule_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=CreateBusinessRule&'+$.param($scope.business_rule),
                        success: function(data){
                            if(data.Success) {
                                if(data.rule_id) {
                                    // For new rules, redirect to edit page
                                    window.location = "#!/BusinessRule/"+data.rule_id;
                                } else {
                                    // For existing rules, refresh the audit data
                                    if($scope.business_rule.rule_id) {
                                        // Reload the business rule details to get updated audit information
                                        jQuery.ajax({
                                            url: host+'businessrules/includes/businessrule_submit.php',
                                            dataType: 'json',
                                            type: 'post',
                                            data: 'ajax=GetBusinessRuleDetails&rule_id='+$scope.business_rule.rule_id,
                                            success: function(auditData){
                                                if(auditData.Success) {
                                                    // Update only the audit fields without affecting form data
                                                    $scope.business_rule.created_date = auditData.Result.created_date;
                                                    $scope.business_rule.updated_date = auditData.Result.updated_date;

                                                    // Format user names for audit fields
                                                    if(auditData.Result.CreatedByFirstName && auditData.Result.CreatedByLastName) {
                                                        $scope.business_rule.created_by = auditData.Result.CreatedByFirstName + ' ' + auditData.Result.CreatedByLastName;
                                                    }

                                                    if(auditData.Result.UpdatedByFirstName && auditData.Result.UpdatedByLastName) {
                                                        $scope.business_rule.updated_by = auditData.Result.UpdatedByFirstName + ' ' + auditData.Result.UpdatedByLastName;
                                                    }

                                                    $scope.$apply();
                                                }
                                            }
                                        });
                                    }

                                    $mdToast.show (
                                        $mdToast.simple()
                                            .content(data.Result)
                                            .action('OK')
                                            .position('right')
                                            .hideDelay(0)
                                            .toastClass('md-toast-success md-block')
                                    );
                                }
                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            $rootScope.$broadcast('preloader:hide');
                            $scope.business_rule.busy = false;
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {
                            $rootScope.$broadcast('preloader:hide');
                            $scope.business_rule.busy = false;
                            initSessionTime(); $scope.$apply();
                        }
                    });

                }

            }
        };
        $scope.SubDispositions = [];
        $scope.GetSubDispositions = function () {
            if($scope.business_rule.business_SubDispositionExists && $scope.business_rule.disposition_id > 0) {
                $scope.SubDispositions = [];
                $scope.business_rule.sub_disposition_id = null;

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'businessrules/includes/businessrule_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetSubDispositions&disposition_id='+$scope.business_rule.disposition_id,
                    success: function(data){
                        if(data.Success) {
                            $scope.SubDispositions = data.Result;
                        } else {
                            $scope.SubDispositions = [];
                            $scope.business_rule.sub_disposition_id = null;
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            } else {
                $scope.SubDispositions = [];
                $scope.business_rule.sub_disposition_id = null;
            }
        };

        $scope.GetSubDispositions1 = function () {
            if($scope.business_rule.business_SubDispositionExists && $scope.business_rule.disposition_id > 0) {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'businessrules/includes/businessrule_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetSubDispositions&disposition_id='+$scope.business_rule.disposition_id,
                    success: function(data){
                        if(data.Success) {
                            $scope.SubDispositions = data.Result;
                        } else {
                            $scope.SubDispositions = [];
                            $scope.business_rule.sub_disposition_id = null;
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });
            } else {
                $scope.SubDispositions = [];
                $scope.business_rule.sub_disposition_id = null;
            }
        };

    });


   /* module.controller("RuleList", function ($scope,$http,$rootScope,$mdToast,$mdDialog,$stateParams) {
        $scope.Rules = [];
        $scope.loading = true;
        $rootScope.$broadcast('preloader:active');
        jQuery.ajax({
            url: host+'businessrules/includes/businessrule_submit.php',
            dataType: 'json',
            type: 'post',
            data: 'ajax=GetRulesList',
            success: function(data){
                if(data.Success) {
                    $scope.Rules = data.Result;
                } else {
                    $scope.Rules = [];
                }
                $scope.loading = false;
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }, error : function (data) {
                $scope.loading = false;
                $rootScope.$broadcast('preloader:hide');
                initSessionTime(); $scope.$apply();
            }
        });

    });*/

    module.controller("RuleList", function ($scope,$location,$http,$rootScope,$mdToast,$mdDialog,$stateParams) {
        $scope.busy = false;
        $scope.RuleList = [];
        $scope.pagedItems = [];
        $scope.RuleVersions = [];

        $scope.GetRuleVersions = function () {
            $rootScope.$broadcast('preloader:active');
            jQuery.ajax({
                url: host+'businessrules/includes/businessrule_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GetRuleVersions',
                success: function(data) {
                    $rootScope.$broadcast('preloader:hide');
                    if(data.Success) {
                        $scope.RuleVersions = data.Result;
                        if($scope.RuleVersions.length > 0) {
                            // Check if version_id is passed as parameter from BusinessRule page
                            if($stateParams.version_id) {
                                // Use the version_id from the parameter
                                $scope.version_id = $stateParams.version_id;
                                $scope.CallServerFunction(0);
                            } else {
                                // Default to current version if no parameter is passed
                                for(var i=0;i<$scope.RuleVersions.length;i++) {
                                    if($scope.RuleVersions[i].current_version == '1') {
                                        $scope.version_id = $scope.RuleVersions[i].version_id;
                                        $scope.CallServerFunction(0);
                                    }
                                }
                            }
                        }
                    } else {
                        $mdToast.show (
                            $mdToast.simple()
                                .content(data.Result)
                                .action('OK')
                                .position('right')
                                .hideDelay(0)
                                .toastClass('md-toast-danger md-block')
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    $rootScope.$broadcast('preloader:hide');
                    alert(data.Result);
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };
        $scope.GetRuleVersions();

        // Function to show Rule Summary Modal
        $scope.showRuleSummaryModal = function(ruleData, ev) {
            $mdDialog.show({
                controller: RuleSummaryModalController,
                templateUrl: 'ruleSummaryModal.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true,
                fullscreen: false,
                locals: {
                    ruleData: ruleData
                }
            });
        };

        // Rule Summary Modal Controller
        function RuleSummaryModalController($scope, $mdDialog, ruleData) {
            $scope.ruleData = ruleData;

            $scope.cancel = function() {
                $mdDialog.cancel();
            };
        }

        $scope.GetVersionStatus = function () {
            if($scope.RuleVersions.length > 0 && $scope.version_id > 0) {
                for(var i=0;i<$scope.RuleVersions.length;i++) {
                    if($scope.RuleVersions[i].version_id == $scope.version_id) {
                        return $scope.RuleVersions[i].status;
                    }
                }
            }
        };

        $scope.canShowMakeCurrentVersion = function () {
            var show = false;
            if($scope.RuleVersions.length > 0 && $scope.version_id > 0) {
                for(var i=0;i<$scope.RuleVersions.length;i++) {
                    if($scope.RuleVersions[i].version_id == $scope.version_id) {
                        if($scope.RuleVersions[i].status == 'Active' && $scope.RuleVersions[i].current_version == '0') {
                            show = true;
                        }
                    }
                }
                return show;
            } else {
                return false;
            }
        };

        $scope.MakeVersionActive = function (ev) {
            if($scope.version_id) {
                var confirm = $mdDialog.confirm()
                .title('Confirmation')
                .content("Rules can't be modified after Status is changed to Active. Are you sure ?")
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Yes')
                .cancel('No, leave it');
                $mdDialog.show(confirm).then(function() {
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host+'businessrules/includes/businessrule_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=MakeVersionActive&version_id='+$scope.version_id,
                        success: function(data) {
                            if(data.Success) {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );

                                if($scope.RuleVersions.length > 0 && $scope.version_id > 0) {
                                    for(var i=0;i<$scope.RuleVersions.length;i++) {
                                        if($scope.RuleVersions[i].version_id == $scope.version_id) {
                                            $scope.RuleVersions[i].status = 'Active';
                                        }
                                    }
                                }
                                $scope.CallServerFunction(0);

                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                                $scope.change_status = false;
                            }
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }
                    });

                }, function() {
                    //$scope.status = 'You decided to keep your debt.';
                    $scope.change_status = false;
                });

            } else {
                $mdToast.show (
                    $mdToast.simple()
                        .content('Invalid Version')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };


        $scope.MakeCurrentVersion = function (ev) {
            if($scope.version_id) {
                var confirm = $mdDialog.confirm()
                .title('Confirmation')
                .content("Are you sure, you want to make this as Current Version ?")
                .ariaLabel('Lucky day')
                .targetEvent(ev)
                .ok('Yes')
                .cancel('No, leave it');
                $mdDialog.show(confirm).then(function() {
                    $rootScope.$broadcast('preloader:active');
                    jQuery.ajax({
                        url: host+'businessrules/includes/businessrule_submit.php',
                        dataType: 'json',
                        type: 'post',
                        data: 'ajax=MakeCurrentVersion&version_id='+$scope.version_id,
                        success: function(data) {
                            if(data.Success) {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-success md-block')
                                );
                                $scope.GetRuleVersions();
                            } else {
                                $mdToast.show (
                                    $mdToast.simple()
                                        .content(data.Result)
                                        .action('OK')
                                        .position('right')
                                        .hideDelay(0)
                                        .toastClass('md-toast-danger md-block')
                                );
                            }
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }, error : function (data) {
                            $rootScope.$broadcast('preloader:hide');
                            initSessionTime(); $scope.$apply();
                        }
                    });

                }, function() {
                    //$scope.status = 'You decided to keep your debt.';
                    $scope.change_status = false;
                });

            } else {
                $mdToast.show (
                    $mdToast.simple()
                        .content('Invalid Version')
                        .action('OK')
                        .position('right')
                        .hideDelay(0)
                        .toastClass('md-toast-danger md-block')
                );
            }
        };


        //Start Pagination Logic
        $scope.itemsPerPage = 10;
        $scope.currentPage = 0;
        $scope.OrderBy = '';
        $scope.OrderByType = '';
        $scope.filter_text = [{}];
        $scope.range = function() {
            var rangeSize = 10;
            var ret = [];
            var start;
            start = $scope.currentPage;
            if ( start > $scope.pageCount()-rangeSize ) {
                start = $scope.pageCount()-rangeSize;
            }
            for (var i=start; i<start+rangeSize; i++) {
                ret.push(i);
            }
            return ret;
        };
        $scope.prevPage = function() {
            if ($scope.currentPage > 0) {
                $scope.currentPage--;
            }
        };
        $scope.firstPage = function () {
            $scope.currentPage = 0;
        };
        $scope.prevPageDisabled = function() {
            return $scope.currentPage === 0 ? "disabled" : "";
        };
        $scope.nextPage = function() {
            if ($scope.currentPage < $scope.pageCount() - 1) {
                $scope.currentPage++;
            }
        };
        $scope.lastPage = function() {
            $scope.currentPage =  $scope.pageCount() - 1;
        };
        $scope.nextPageDisabled = function() {
            return $scope.currentPage === $scope.pageCount() - 1 ? "disabled" : "";
        };
        $scope.pageCount = function() {
            return Math.ceil($scope.total/$scope.itemsPerPage);
        };
        $scope.setPage = function(n) {
            if (n >= 0 && n < $scope.pageCount()) {
                $scope.currentPage = n;
            }
        };
        $scope.CallServerFunction = function (newValue) {
            //if($scope.CurrentStatus != '' )  {
            if($scope.version_id > 0) {
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'businessrules/includes/businessrule_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetRulesList&limit='+$scope.itemsPerPage+'&skip='+newValue*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text))+'&version_id='+$scope.version_id,
                    success: function(data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            // Process the data to format user names
                            $scope.pagedItems = data.Result.map(function(item) {
                                // Format created_by and updated_by names
                                if(item.CreatedByFirstName && item.CreatedByLastName) {
                                    item.created_by = item.CreatedByFirstName + ' ' + item.CreatedByLastName;
                                } else {
                                    item.created_by = item.created_by || '';
                                }

                                if(item.UpdatedByFirstName && item.UpdatedByLastName) {
                                    item.updated_by = item.UpdatedByFirstName + ' ' + item.UpdatedByLastName;
                                } else {
                                    item.updated_by = item.updated_by || '';
                                }

                                return item;
                            });

                            if(data.total) {
                                $scope.total = data.total;
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        alert(data.Result);
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };

       /* $scope.ExportGenerateBusinessRulesList = function () {
            alert("1");
            jQuery.ajax({
                url: host+'businessrules/includes/businessrule_submit.php',
                dataType: 'json',
                type: 'post',
                //data: 'ajax=GenerateBusinessRulesListXLS',
                data: 'ajax=GenerateBusinessRulesListXLS&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text)),
                success: function(data) {
                    if(data.Success) {
                        alert("2");
                        window.location="templates/BusinessRulesListxls.php";
                    } else {
                        alert("4");
                        //yaaaService.addAlert('',data.Result,5,'danger','dir1');
                        $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .position('right')
                                .hideDelay(3000)
                        );
                }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    //alert(data.Result);
                    alert("3");
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };*/

        /*$scope.ExportGenerateBusinessRulesList = function (id) {
            //alert("1");
            //alert(id);
            jQuery.ajax({
                url: host+'businessrules/includes/businessrule_submit.php',
                dataType: 'json',
                type: 'post',
                data: 'ajax=GenerateBusinessRulesListXLS&rule_id='+id,
                success: function(data) {
                    if(data.Success) {
                        //alert("2");
                        window.location="templates/BusinessRulesListxls.php";
                    } else {
                        //alert("4");
                    $mdToast.show(
                            $mdToast.simple()
                                .content(data.Result)
                                .position('right')
                                .hideDelay(3000)
                        );
                    }
                    initSessionTime(); $scope.$apply();
                }, error : function (data) {
                    //alert(data.Result);
                    //alert("3");
                    $scope.error = data;
                    initSessionTime(); $scope.$apply();
                }
            });
        };*/

        $scope.$watch("currentPage", function(newValue, oldValue) {
            $scope.CallServerFunction(newValue);
        });
        $scope.convertSingle = function (multiarray) {
            var result = {};
            for(var i=0;i<multiarray.length;i++) {
                result[i] = multiarray[i];
            }
            //alert(result);
            return result;
        };
        $scope.MakeOrderBy = function (orderby) {
            if($scope.version_id > 0) {
                $scope.OrderBy = orderby;
                if($scope.OrderByType == 'asc') {
                    $scope.OrderByType = 'desc';
                } else {
                    $scope.OrderByType = 'asc';
                }
                $scope.busy = true;
                $rootScope.$broadcast('preloader:active');

                jQuery.ajax({
                    url: host+'businessrules/includes/businessrule_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GetRulesList&limit='+$scope.itemsPerPage+'&skip='+$scope.currentPage*$scope.itemsPerPage+'&OrderBy='+$scope.OrderBy+'&OrderByType='+$scope.OrderByType+'&'+$.param($scope.convertSingle($scope.filter_text))+'&version_id='+$scope.version_id,
                    success: function(data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        if(data.Success) {
                            // Process the data to format user names
                            $scope.pagedItems = data.Result.map(function(item) {
                                // Format created_by and updated_by names
                                if(item.CreatedByFirstName && item.CreatedByLastName) {
                                    item.created_by = item.CreatedByFirstName + ' ' + item.CreatedByLastName;
                                } else {
                                    item.created_by = item.created_by || '';
                                }

                                if(item.UpdatedByFirstName && item.UpdatedByLastName) {
                                    item.updated_by = item.UpdatedByFirstName + ' ' + item.UpdatedByLastName;
                                } else {
                                    item.updated_by = item.updated_by || '';
                                }

                                return item;
                            });

                            if(data.total) {
                                $scope.total = data.total;
                            }
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                            );
                        }
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $scope.busy = false;
                        $rootScope.$broadcast('preloader:hide');
                        $scope.error = data;
                        initSessionTime(); $scope.$apply();
                    }
                });
            }
        };
        $scope.MakeFilter = function () {
            if($scope.currentPage == 0) {
                $scope.CallServerFunction($scope.currentPage);
            } else {
                $scope.currentPage = 0;
            }
        };

        // Show export dialog
        $scope.showExportDialog = function(ev) {
            $mdDialog.show({
                controller: ExportDialogController,
                templateUrl: 'exportDialog.html',
                parent: angular.element(document.body),
                targetEvent: ev,
                clickOutsideToClose: true,
                locals: {
                    RuleVersions: $scope.RuleVersions,
                    OrderBy: $scope.OrderBy,
                    OrderByType: $scope.OrderByType,
                    filter_text: $scope.filter_text
                }
            });
        };

        // Export Dialog Controller
        function ExportDialogController($scope, $mdDialog, RuleVersions, OrderBy, OrderByType, filter_text) {
            $scope.RuleVersions = angular.copy(RuleVersions);
            $scope.selectAllVersions = false;

            // Initialize selection state
            $scope.RuleVersions.forEach(function(version) {
                version.selected = false;
            });

            $scope.toggleAllVersions = function() {
                $scope.RuleVersions.forEach(function(version) {
                    version.selected = $scope.selectAllVersions;
                });
            };

            $scope.updateSelectAll = function() {
                var selectedCount = $scope.RuleVersions.filter(function(v) { return v.selected; }).length;
                $scope.selectAllVersions = selectedCount === $scope.RuleVersions.length;
            };

            $scope.getSelectedVersionsCount = function() {
                return $scope.RuleVersions.filter(function(v) { return v.selected; }).length;
            };

            $scope.cancel = function() {
                $mdDialog.cancel();
            };

            $scope.exportSelectedVersions = function() {
                var selectedVersions = $scope.RuleVersions.filter(function(v) { return v.selected; });
                if (selectedVersions.length === 0) {
                    return;
                }

                var versionIds = selectedVersions.map(function(v) { return v.version_id; });

                jQuery.ajax({
                    url: host+'businessrules/includes/businessrule_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=GenerateBusinessRulesListXLS&version_ids=' + versionIds.join(',') + '&OrderBy=' + OrderBy + '&OrderByType=' + OrderByType + '&' + $.param(convertSingle(filter_text)),
                    success: function(data) {
                        if(data.Success) {
                            window.location="templates/BusinessRulesListxls.php";
                            $mdDialog.hide();
                        } else {
                            $mdToast.show(
                                $mdToast.simple()
                                    .content(data.Result)
                                    .position('right')
                                    .hideDelay(3000)
                                    .toastClass('md-toast-danger')
                            );
                        }
                    },
                    error: function (data) {
                        $mdToast.show(
                            $mdToast.simple()
                                .content('Export failed. Please try again.')
                                .position('right')
                                .hideDelay(3000)
                                .toastClass('md-toast-danger')
                        );
                    }
                });
            };

            // Helper function for filter conversion
            function convertSingle(data) {
                var result = {};
                if (data && data[0]) {
                    for (var key in data[0]) {
                        if (data[0].hasOwnProperty(key)) {
                            result[key] = data[0][key];
                        }
                    }
                }
                return result;
            }
        }

        // Keep the old function for backward compatibility
        $scope.BusinessRulesListxls = function () {
            // Use the new dialog instead
            $scope.showExportDialog();
        };
        //End Pagination Logic

        $scope.ChangeRuleStatus = function (rule,ev) {
            var confirm = $mdDialog.confirm()
            .title('Confirmation')
            .content('Are you sure you want to change the status of the Business Rule?')
            .ariaLabel('Lucky day')
            .targetEvent(ev)
            .ok('Yes')
            .cancel('No, leave it');
            $mdDialog.show(confirm).then(function() {
                rule.loading = true;

                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'businessrules/includes/businessrule_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=ChangeRuleStatus&'+$.param(rule),
                    success: function(data){
                        if(data.Success) {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                            if(rule.status == 'Active') {
                                rule.status = 'Inactive'
                            } else {
                                rule.status = 'Active'
                            }
                        }
                        $rootScope.$broadcast('preloader:hide');
                        rule.loading = false;
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        rule.loading = false;
                        initSessionTime(); $scope.$apply();
                    }
                });

            }, function() {
                //$scope.status = 'You decided to keep your debt.';
                if(rule.status == 'Active') {
                    rule.status = 'Inactive'
                } else {
                    rule.status = 'Active'
                }
            });
        };


        $scope.CreateNewBRVersion = function (ev) {
            var confirm = $mdDialog.confirm()
            .title('Confirmation')
            .content('Are you sure you want to create new Version for Business Rules ?')
            .ariaLabel('Lucky day')
            .targetEvent(ev)
            .ok('Yes')
            .cancel('No, leave it');
            $mdDialog.show(confirm).then(function() {
                $rootScope.$broadcast('preloader:active');
                jQuery.ajax({
                    url: host+'businessrules/includes/businessrule_submit.php',
                    dataType: 'json',
                    type: 'post',
                    data: 'ajax=CreateNewBRVersion&version_name='+$scope.version_name+'&version_description='+$scope.version_description,
                    success: function(data){
                        if(data.Success) {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-success md-block')
                            );
                            if(data.version) {
                                $scope.RuleVersions.push(data.version);
                            }
                            if(data.version_id) {
                                $scope.version_id = data.version_id;
                                $scope.BREVersionChanged();
                            }
                            $scope.version_name = '';
                            $scope.version_description = '';
                            $scope.newversionpannel = false;
                        } else {
                            $mdToast.show (
                                $mdToast.simple()
                                    .content(data.Result)
                                    .action('OK')
                                    .position('right')
                                    .hideDelay(0)
                                    .toastClass('md-toast-danger md-block')
                            );
                        }
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }, error : function (data) {
                        $rootScope.$broadcast('preloader:hide');
                        initSessionTime(); $scope.$apply();
                    }
                });

            }, function() {
            });
        };

        $scope.BREVersionChanged = function() {
            $scope.CallServerFunction(0);
        };
    });

})();





