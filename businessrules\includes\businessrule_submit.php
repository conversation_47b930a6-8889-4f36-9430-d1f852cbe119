<?php
	session_start();
	include_once("../database/businessrule.class.php");
	$obj = new BusinessRuleClass();

	if($_POST['ajax'] == "GetDispositions") {
		$result = $obj->GetDispositions($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetWorkflows") {
		$result = $obj->GetWorkflows($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetInputResults") {
		$result = $obj->GetInputResults($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetBusinessRuleAttributes") {
		$result = $obj->GetBusinessRuleAttributes($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetAttributeValues") {
		$result = $obj->GetAttributeValues($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CreateBusinessRule") {
		$result = $obj->CreateBusinessRule($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GenerateBusinessRulesListXLS") {
		$result = $obj->GenerateBusinessRulesListXLS($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetRulesList") {
		$result = $obj->GetRulesList($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "GetSubDispositions") {
		$result = $obj->GetSubDispositions($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "ChangeRuleStatus") {
		$result = $obj->ChangeRuleStatus($_POST);
		echo $result;
	}
	if($_POST['ajax'] == "ArchiveRule") {
		$result = $obj->ArchiveRule($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetBusinessRuleDetails") {
		$result = $obj->GetBusinessRuleDetails($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetRuleVersions") {
		$result = $obj->GetRuleVersions($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "CreateNewBRVersion") {
		$result = $obj->CreateNewBRVersion($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "MakeVersionActive") {
		$result = $obj->MakeVersionActive($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "MakeCurrentVersion") {
		$result = $obj->MakeCurrentVersion($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetPendingBRVersions") {
		$result = $obj->GetPendingBRVersions($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetAWSCustomers") {
		$result = $obj->GetAWSCustomers($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetPartTypes") {
		$result = $obj->GetPartTypes($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetSourceTypes") {
		$result = $obj->GetSourceTypes($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetMaterialTypes") {
		$result = $obj->GetMaterialTypes($_POST);
		echo $result;
	}

	if($_POST['ajax'] == "GetAllPartTypes") {
		$result = $obj->GetAllPartTypes($_POST);
		echo $result;
	}

?>