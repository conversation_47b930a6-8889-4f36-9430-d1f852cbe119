<?php
session_start();
include_once("speed.class.php");
class PendingBinAuditClass extends SpeedClass {

    public function GetBinAuditBinsList($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => 'No data'
			);

            if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Pending Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Pending Bin Audit Page';
				return json_encode($json);
			}

            $query = "select r.*,f.FacilityName,d.disposition,c.BinName,DATEDIFF(NOW(),r.CreatedDate) as days_assigned from bin_audit_bins r 
            left join custompallet c on r.CustomPalletID = c.CustomPalletID 
            left join facility f on f.FacilityID = c.FacilityID 
            left join disposition d on d.disposition_id = c.disposition_id
            where r.AuditLocked = 1 and r.FacilityID = '".$_SESSION['user']['FacilityID']."' ";

			if($data[0] && count($data[0]) > 0) {
				foreach ($data[0] as $key => $value) {
					if($value != '') {
						if($key == 'BinName') {
							$query = $query . " AND c.BinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'disposition') {
							$query = $query . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
						if($key == 'FacilityName') {
							$query = $query . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}						
						if($key == 'Status') {
							$query = $query . " AND r.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}                        
                        if($key == 'CreatedDate') {
							$query = $query . " AND r.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
						}
					}
				}
			}
			if($data['OrderBy'] != '') {
				if($data['OrderByType'] == 'asc') {
					$order_by_type = 'asc';
				} else {
					$order_by_type = 'desc';
				}

				if($data['OrderBy'] == 'BinName') {
					$query = $query . " order by c.BinName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'disposition') {
					$query = $query . " order by d.disposition ".$order_by_type." ";
				} else if($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by f.FacilityName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'Status') {
					$query = $query . " order by r.Status ".$order_by_type." ";
				} else if($data['OrderBy'] == 'CreatedDate') {
					$query = $query . " order by r.CreatedDate ".$order_by_type." ";
				} 
			} else {
				$query = $query . " order by r.CreatedDate asc ";
			}

			$query = $query . " limit ".intval(mysqli_real_escape_string($this->connectionlink,$data['skip'])).",".intval(mysqli_real_escape_string($this->connectionlink,$data['limit']));

            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $i = 0;
                while($row = mysqli_fetch_assoc($q)) {
                    $result[$i] = $row;
                    $i++;
                }
                $json['Success'] = true;
                $json['Result'] = $result;
            } else {
                $json['Success'] = false;
                $json['Result'] = "No Records Available";
            }

            if($data['skip'] == 0) {

                $query1 = "select count(*) from bin_audit_bins r 
                left join custompallet c on r.CustomPalletID = c.CustomPalletID 
                left join facility f on f.FacilityID = c.FacilityID 
                left join disposition d on d.disposition_id = c.disposition_id
                where r.AuditLocked = 1 and r.FacilityID = '".$_SESSION['user']['FacilityID']."' ";
                if($data[0] && count($data[0]) > 0) {
                    foreach ($data[0] as $key => $value) {
                        if($value != '') {

                            if($key == 'BinName') {
                                $query1 = $query1 . " AND c.BinName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'disposition') {
                                $query1 = $query1 . " AND d.disposition like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }
                            if($key == 'FacilityName') {
                                $query1 = $query1 . " AND f.FacilityName like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }						
                            if($key == 'Status') {
                                $query1 = $query1 . " AND r.Status like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }                        
                            if($key == 'CreatedDate') {
                                $query1 = $query1 . " AND r.CreatedDate like '%".mysqli_real_escape_string($this->connectionlink,$value)."%' ";
                            }

                        }
                    }
                }

                $q1 = mysqli_query($this->connectionlink,$query1);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row1 = mysqli_fetch_assoc($q1);
                    $count = $row1['count(*)'];
                }
                $json['total'] = $count;
			}
			return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}


    public function GetBinAuditBinsDetails($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => 'No data'
			);

            if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Audit Page';
				return json_encode($json);
			}

            if($data['ID']) {                
                //$query = "select a.*,c.BinName,c.AssetsCount,c.disposition_id from bin_audit_bins a,custompallet c where a.ID = '".mysqli_real_escape_string($this->connectionlink,$data['ID'])."' and a.CustomPalletID = c.CustomPalletID";
                $query = "select a.*,c.BinName,c.AssetsCount,c.disposition_id,d.disposition,d.color_code from bin_audit_bins a,custompallet c,disposition d where a.ID = '".mysqli_real_escape_string($this->connectionlink,$data['ID'])."' and c.disposition_id = d.disposition_id and a.CustomPalletID = c.CustomPalletID";                
                $q = mysqli_query($this->connectionlink,$query);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }

                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row = mysqli_fetch_assoc($q);
                    if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
                        $json['Success'] = false;
                        $json['Result'] = 'Facility Mismatch';
                        return json_encode($json);
                    }
                    if($row['AuditedBy'] > 0) {
                        $json['Success'] = false;
                        $json['Result'] = 'Audit Completed for the BIN';
                        return json_encode($json);
                    }

                    if($row['AuditLocked'] == 0) {
                        $json['Success'] = false;
                        $json['Result'] = 'Bin released from the Audit list';
                        return json_encode($json);
                    }

                    $json['Success'] = true;
                    $json['Result'] = $row;
                    return json_encode($json);
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'Invalid ID';
                    return json_encode($json);
                }
            } else {
                $json['Success'] = false;
				$json['Result'] = 'Invalid input details';
				return json_encode($json);
            }

            return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}


    public function ValidateBinAuditSameDispositionBin($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => 'No data'
			);

            if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Audit Page';
				return json_encode($json);
			}

            if($data['SameDispositionBinName'] != '') {                
                $query = "select c.* from custompallet c where c.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['SameDispositionBinName'])."' ";
                $q = mysqli_query($this->connectionlink,$query);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }

                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row = mysqli_fetch_assoc($q);

                    if($row['StatusID'] != '1') {
                        $json['Success'] = false;
                        $json['Result'] = 'Bin Status is not active';
                        return json_encode($json);
                    }

                    if($row['disposition_id'] != $data['SourceDisposition']) {
                        $json['Success'] = false;
                        $json['Result'] = 'Disposition is not matching with Source BIN';
                        return json_encode($json);
                    }

                    if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
                        $json['Success'] = false;
                        $json['Result'] = 'Facility Mismatch';
                        return json_encode($json);
                    }

                    if($row['AuditLocked'] == 1) {
                        $json['Success'] = false;
                        $json['Result'] = 'Bin is added to Pending Audit List';
                        return json_encode($json);
                    }

                    $json['Success'] = true;
                    $json['Result'] = $row;
                    return json_encode($json);
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'Invalid Output Bin';
                    return json_encode($json);
                }
            } else {
                $json['Success'] = false;
				$json['Result'] = 'Invalid input details';
				return json_encode($json);
            }

            return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}


    public function ValidateBinAuditAllDispositionBin($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => 'No data'
			);

            if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Audit Page';
				return json_encode($json);
			}

            if($data['AllDispositionBinName'] != '') {                
                $query = "select c.* from custompallet c where c.BinName = '".mysqli_real_escape_string($this->connectionlink,$data['AllDispositionBinName'])."' ";
                $q = mysqli_query($this->connectionlink,$query);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }

                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row = mysqli_fetch_assoc($q);

                    if($row['StatusID'] != '1') {
                        $json['Success'] = false;
                        $json['Result'] = 'Bin Status is not active';
                        return json_encode($json);
                    }

                    if($row['AcceptAllDisposition'] != '1') {
                        $json['Success'] = false;
                        $json['Result'] = 'Bin is not All Disposition Bin';
                        return json_encode($json);
                    }

                    if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
                        $json['Success'] = false;
                        $json['Result'] = 'Facility Mismatch';
                        return json_encode($json);
                    }

                    if($row['AuditLocked'] == 1) {
                        $json['Success'] = false;
                        $json['Result'] = 'Bin is added to Pending Audit List';
                        return json_encode($json);
                    }

                    $json['Success'] = true;
                    $json['Result'] = $row;
                    return json_encode($json);
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'Invalid Output Bin';
                    return json_encode($json);
                }
            } else {
                $json['Success'] = false;
				$json['Result'] = 'Invalid input details';
				return json_encode($json);
            }

            return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}


    public function ValidateBinAuditSourceBin($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => 'No data'
			);

            if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Audit Page';
				return json_encode($json);
			}

            $query1 = "select * from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['SourceBinName'])."' ";
            $q1 = mysqli_query($this->connectionlink,$query1);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row1 = mysqli_fetch_assoc($q1);

                if($row1['FacilityID'] != $_SESSION['user']['FacilityID']) {
                    $json['Success'] = false;
                    $json['Result'] = 'Facility Mismatch';
                    return json_encode($json);
                }

                if($row1['AuditLocked'] == 0) {
                    $json['Success'] = false;
                    $json['Result'] = 'Bin is not listed in Pending Audit Bins';
                    return json_encode($json);
                }
                $CustomPalletID = $row1['CustomPalletID'];

            } else {
                $json['Success'] = false;
                $json['Result'] = 'Invalid Source Bin';
                return json_encode($json);
            }



            //if($data['ID']) {
            if($CustomPalletID > 0) {
                //$query = "select a.*,c.BinName,c.AssetsCount,c.disposition_id from bin_audit_bins a,custompallet c where a.ID = '".mysqli_real_escape_string($this->connectionlink,$data['ID'])."' and a.CustomPalletID = c.CustomPalletID";
                //$query = "select a.*,c.BinName,c.AssetsCount,c.disposition_id from bin_audit_bins a,custompallet c where a.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."' and a.CustomPalletID = c.CustomPalletID and a.AuditLocked = '1' ";
                $query = "select a.*,c.BinName,c.AssetsCount,c.disposition_id,d.disposition,d.color_code from bin_audit_bins a,custompallet c,disposition d where a.CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$CustomPalletID)."' and a.CustomPalletID = c.CustomPalletID and c.disposition_id = d.disposition_id and a.AuditLocked = '1' ";
                $q = mysqli_query($this->connectionlink,$query);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }

                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row = mysqli_fetch_assoc($q);
                    if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
                        $json['Success'] = false;
                        $json['Result'] = 'Facility Mismatch';
                        return json_encode($json);
                    }
                    if($row['AuditedBy'] > 0) {
                        $json['Success'] = false;
                        $json['Result'] = 'Audit Completed for the BIN';
                        return json_encode($json);
                    }

                    if($row['AuditLocked'] == 0) {
                        $json['Success'] = false;
                        $json['Result'] = 'Bin released from the Audit list';
                        return json_encode($json);
                    }

                    $row['Accuracy'] = floatval($row['Accuracy']);
                    $row['AccuracyTarget'] = floatval($row['AccuracyTarget']);

                    $json['Success'] = true;
                    $json['Result'] = $row;
                    return json_encode($json);
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'Invalid ID';
                    return json_encode($json);
                }
            } else {
                $json['Success'] = false;
				$json['Result'] = 'Invalid input details';
				return json_encode($json);
            }

            return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}


    public function ValidateBinAuditSerial($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data
			);            

            if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Audit Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Bin Audit Page';
				return json_encode($json);
			}

            //Start check Serial Details
            $query = "select a.*,d.disposition,d.color_code from asset a 
            left join disposition d on a.disposition_id = d.disposition_id 
            where a.SerialNumber = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."' order by a.StatusID ";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row = mysqli_fetch_assoc($q);
                if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
                    $json['Success'] = false;
                    $json['Result'] = 'Facility Mismatch';
                    return json_encode($json);    
                }
                if($row['StatusID'] != '1') {
                    // $json['Success'] = false;
                    // $json['Result'] = 'Serial Status is not Active';
                    // return json_encode($json);    
                    $json['SerialNumberStatus'] = 'Not Active';
                } else {
                    $json['SerialNumberStatus'] = 'Active';
                }

                if($row['CustomPalletID'] != $data['SourceBinID']) {
                    $json['BinMismatch'] = '1';
                }

                if($row['disposition_id'] != $data['SourceDisposition']) {
                    $json['DispositionMismatch'] = '1';
                }

                $json['Success'] = true;
                $json['Result'] = $row;
                return json_encode($json);

            } else {
                $json['Success'] = false;
                $json['Result'] = 'Invalid Serial';
                return json_encode($json);
            }
            //End check Serial Details

            return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}


    public function ValidateBinAuditMPN($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => ''
			);            

            if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Audit Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Bin Audit Page';
				return json_encode($json);
			}

            if($data['MPN']) {
                $exact_mpn = $this->GetExactMPN($data['MPN']);
				if($exact_mpn['Success'] == true) {					
                    $json['MPN'] = $exact_mpn['MPN'];
				} else {
                    $json['Success'] = false;
                    $json['Result'] = $exact_mpn['Error'];
                    return json_encode($json);
                }
            }
            
        
            
            //Start check Serial Details
            $query = "select a.* from asset a where a.AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."' ";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row = mysqli_fetch_assoc($q);
                if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
                    $json['Success'] = false;
                    $json['Result'] = 'Facility Mismatch';
                    return json_encode($json);    
                }
                // if($row['StatusID'] != '1') {
                //     $json['Success'] = false;
                //     $json['Result'] = 'Serial Status is not Active';
                //     return json_encode($json);    
                // }

                if($row['UniversalModelNumber'] != $data['MPN']) {
                    $json['MPNMismatch'] = '1';
                }
                $json['Success'] = true;
                $json['Result'] = $row;
                return json_encode($json);

            } else {
                $json['Success'] = false;
                $json['Result'] = 'Invalid Serial';
                return json_encode($json);
            }
            //End check Serial Details

            return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}


    public function AuditBinSerial($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data
			);            
            //return json_encode($json);
            if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Audit Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Bin Audit Page';
				return json_encode($json);
			}

            if($data['MPN']) {
                $exact_mpn = $this->GetExactMPN($data['MPN']);
				if($exact_mpn['Success'] == true) {					
                    $data['MPN'] = $exact_mpn['MPN'];
				} else {
                    $json['Success'] = false;
                    $json['Result'] = $exact_mpn['Error'];
                    return json_encode($json);
                }
            }

            //Start get asset details
            //$query11 = "select * from asset where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."' ";
            $query11 = "select a.*,p.AWSCustomerID,p.WasteClassificationType from asset a 
            left join pallets p on a.idPallet = p.idPallet 
            where a.AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."' ";
            $q11 = mysqli_query($this->connectionlink,$query11);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $old = mysqli_fetch_assoc($q11);
                if($old['UpdatedBy'] > 0){
                    $last_touched_by = $old['UpdatedBy'];
                } else if($old['CreatedBy'] > 0){
                    $last_touched_by = $old['CreatedBy'];
                } else  {
                    $last_touched_by = 0;
                }
            } else {
                $json['Success'] = false;
                $json['Result'] = 'Invalid Serial';
                return json_encode($json);
            }
            //End get asset details
            
            
            //Start update Asset
            $query = "update asset set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['NewBinID'])."',disposition_id = '".mysqli_real_escape_string($this->connectionlink,$data['NewDispositionID'])."',UniversalModelNumber = '".mysqli_real_escape_string($this->connectionlink,$data['MPN'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."' ";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }

            $query1 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['NewBinID'])."' where AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."'";
            $q1 = mysqli_query($this->connectionlink,$query1);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }

            //End update Asset

            //Start update CustomPallet counts
            $query2 = "update custompallet set AssetsCount = AssetsCount + 1,LastModifiedDate = NOW(),LastModifiedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['NewBinID'])."'";
            $q2 = mysqli_query($this->connectionlink,$query2);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }


            $query2 = "update custompallet set AssetsCount = AssetsCount - 1,LastModifiedDate = NOW(),LastModifiedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['CurrentBinID'])."'";
            $q2 = mysqli_query($this->connectionlink,$query2);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            //End update CustomPallet counts

            //Start update asset tracking
            $desc = "Asset Moved to BIN, (BIN ID : ".$data['NewBinName'].") in Bin Audit Screen";

            if($old['UniversalModelNumber'] != $data['MPN']) { // MPN Changed
                $desc = $desc . " , Asset MPN Updated from ".$old['UniversalModelNumber']." to ".$data['MPN'];
            }

			$query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) values ('".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."')";
			$q3 = mysqli_query($this->connectionlink,$query3);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
            //End update asset tracking

            //If audit_notes_scan_time is nulll or not available copy origin_bin_scan_time to audit_notes_scan_time
            if(empty($data['audit_notes_scan_time']) || $data['audit_notes_scan_time'] == '') {
                $data['audit_notes_scan_time'] = $data['origin_bin_scan_time'];
            }

            //Start insert in bin_audit_records
            $event_id = rand(1000000000, 9999999999);
			$batch_event_flag = 'N';

            //Start get Classification code and classification type
            $classification = $this->GetClassificationDetails($data['AssetScanID'],$data['NewDispositionID']);
            if($classification['Success'] == true) {
                $data['WasteCode'] = $classification['WasteCode'];
                $data['WasteClassificationType'] = $classification['WasteClassificationType'];
            } else {
                $data['WasteCode'] = 'n/a';
                $data['WasteClassificationType'] = 'n/a';
            }
            //End get Classification code and classification type

            $query4 = "insert into bin_audit_records (BinAuditID, ControlID, SourceBinID, SourceBinName, SourceDisposition, SameDispositionBinID, SameDispositionBinName, AllDispositionBinID, AllDispositionBinName, SerialNumber, MPN, EvaluationResult, NewBinID, NewBinName, CreatedDate, CreatedBy, AssetScanID,NewDispositionID,NewDispositionName,AuditNotes,Damaged,origin_bin_scan_time,serial_scan_time,origin_mpn_id,mpn_scan_time,part_type,audit_notes_scan_time,EvaluationResult_scan_time,valid_serial_flag,valid_mpn_flag,assigned_bin_scan_time,last_touched_by,event_id,batch_event_flag,AWSCustomerID,WasteClassificationType,WasteCode) values (
                '".mysqli_real_escape_string($this->connectionlink,$data['BinAuditID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ControlID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SourceBinName'])."','".mysqli_real_escape_string($this->connectionlink,$data['SourceDisposition'])."','".mysqli_real_escape_string($this->connectionlink,$data['SameDispositionBinID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SameDispositionBinName'])."','".mysqli_real_escape_string($this->connectionlink,$data['AllDispositionBinID'])."','".mysqli_real_escape_string($this->connectionlink,$data['AllDispositionBinName'])."','".mysqli_real_escape_string($this->connectionlink,$data['SerialNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['MPN'])."','".mysqli_real_escape_string($this->connectionlink,$data['EvaluationResult'])."','".mysqli_real_escape_string($this->connectionlink,$data['NewBinID'])."','".mysqli_real_escape_string($this->connectionlink,$data['NewBinName'])."',NOW(),'".$_SESSION['user']['UserId']."','".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."','".mysqli_real_escape_string($this->connectionlink,$data['NewDispositionID'])."','".mysqli_real_escape_string($this->connectionlink,$data['NewDispositionName'])."','".mysqli_real_escape_string($this->connectionlink,$data['AuditNotes'])."','".mysqli_real_escape_string($this->connectionlink,$data['Damaged'])."','".mysqli_real_escape_string($this->connectionlink,$data['origin_bin_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['serial_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$old['UniversalModelNumber'])."','".mysqli_real_escape_string($this->connectionlink,$data['mpn_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$old['part_type'])."','".mysqli_real_escape_string($this->connectionlink,$data['audit_notes_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['EvaluationResult_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$data['valid_serial_flag'])."','".mysqli_real_escape_string($this->connectionlink,$data['valid_mpn_flag'])."','".mysqli_real_escape_string($this->connectionlink,$data['assigned_bin_scan_time'])."','".mysqli_real_escape_string($this->connectionlink,$last_touched_by)."','".$event_id."','".$batch_event_flag."','".mysqli_real_escape_string($this->connectionlink,$old['AWSCustomerID'])."','".mysqli_real_escape_string($this->connectionlink,$data['WasteClassificationType'])."','".mysqli_real_escape_string($this->connectionlink,$data['WasteCode'])."')";
            $q4 = mysqli_query($this->connectionlink,$query4);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
            $json['AuditRecordID'] = mysqli_insert_id($this->connectionlink);
            //End insert in bin_audit_records

            //Start update accuracy 
            if($data['EvaluationResult'] == 'Pass-Audit') {
                $query6 = "update bin_audit_bins set SuccessfulAssets = SuccessfulAssets + 1 where ID = '".mysqli_real_escape_string($this->connectionlink,$data['BinAuditID'])."' ";
                $q6 = mysqli_query($this->connectionlink,$query6);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }

                $query6 = "update bin_audit_bins set Accuracy = (SuccessfulAssets / TotalAssets) * 100 where ID = '".mysqli_real_escape_string($this->connectionlink,$data['BinAuditID'])."' ";
                $q6 = mysqli_query($this->connectionlink,$query6);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
            }
            //End update accuracy

            //Start get Accuracy
            $query7 = "select Accuracy,AccuracyTarget from bin_audit_bins where ID = '".mysqli_real_escape_string($this->connectionlink,$data['BinAuditID'])."' ";
            $q7 = mysqli_query($this->connectionlink,$query7);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row7 = mysqli_fetch_assoc($q7);
                $json['Accuracy'] = floatval($row7['Accuracy']);
                $json['AccuracyTarget'] = floatval($row7['AccuracyTarget']);
            }
            //End get Accuracy

            //Start get bin counts
            $query5 = "select AssetsCount from custompallet where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."'";
            $q5 = mysqli_query($this->connectionlink,$query5);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row5 = mysqli_fetch_assoc($q5);
                $json['SourceBinCount'] = $row5['AssetsCount'];
            }

            $query5 = "select AssetsCount from custompallet where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['SameDispositionBinID'])."'";
            $q5 = mysqli_query($this->connectionlink,$query5);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row5 = mysqli_fetch_assoc($q5);
                $json['SameDispositionBinCount'] = $row5['AssetsCount'];
            }

            $query5 = "select AssetsCount from custompallet where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['AllDispositionBinID'])."'";
            $q5 = mysqli_query($this->connectionlink,$query5);
			if(mysqli_error($this->connectionlink)) {
				$json['Success'] = false;
				$json['Result'] = mysqli_error($this->connectionlink);
				return json_encode($json);
			}
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row5 = mysqli_fetch_assoc($q5);
                $json['AllDispositionBinCount'] = $row5['AssetsCount'];
            }
            //End get in counts

            $json['Success'] = true;
			$json['Result'] = 'Serial moved to new Bin';
            return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}


    public function MoveSerialsToOutputBin($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data
			);            
            //return json_encode($json);
            if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Audit Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Bin Audit Page';
				return json_encode($json);
			}

            //Start validate OutputBIN
            $query = "select * from custompallet where BinName = '".mysqli_real_escape_string($this->connectionlink,$data['OutputBin'])."' ";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row = mysqli_fetch_assoc($q);
                if($row['FacilityID'] != $_SESSION['user']['FacilityID']) {
                    $json['Success'] = false;
                    $json['Result'] = 'Facility Mismatch';
                    return json_encode($json);
                }

                if($row['StatusID'] != '1') {
                    $json['Success'] = false;
                    $json['Result'] = 'Bin Status is not active';
                    return json_encode($json);
                }

                if($row['AcceptAllDisposition'] != '1') {
                    $json['Success'] = false;
                    $json['Result'] = 'Bin is not All Disposition BIN';
                    return json_encode($json);
                }
            } else {
                $json['Success'] = false;
                $json['Result'] = 'Invalid BIN';
                return json_encode($json);
            }
            //End validate OutputBIN


            //Start update asset tracking
            $desc = "Asset Moved to BIN, (BIN ID : ".$row['BinName'].") in Bin Audit Screen";
            $query3 = "insert into asset_tracking (AssetScanID,Action,Description,UniqueID,CreatedDate,CreatedBy) 
            select AssetScanID,'".mysqli_real_escape_string($this->connectionlink,$desc)."','','',NOW(),'".$_SESSION['user']['UserId']."' from asset where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."'";
            $q3 = mysqli_query($this->connectionlink,$query3);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            //End update asset tracking
            
 
 
            //Start insert in bin_audit_records

            $event_id = rand(1000000000, 9999999999);
			$batch_event_flag = 'Y';            

            $query4 = "insert into bin_audit_records (BinAuditID, ControlID, SourceBinID, SourceBinName, SourceDisposition, SameDispositionBinID, SameDispositionBinName, AllDispositionBinID, AllDispositionBinName, SerialNumber, MPN, EvaluationResult, NewBinID, NewBinName, CreatedDate, CreatedBy, AssetScanID,NewDispositionID,NewDispositionName,event_id,batch_event_flag) 
            select '".mysqli_real_escape_string($this->connectionlink,$data['BinAuditID'])."','".mysqli_real_escape_string($this->connectionlink,$data['ControlID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SourceBinName'])."','".mysqli_real_escape_string($this->connectionlink,$data['SourceDisposition'])."','".mysqli_real_escape_string($this->connectionlink,$data['SameDispositionBinID'])."','".mysqli_real_escape_string($this->connectionlink,$data['SameDispositionBinName'])."','".mysqli_real_escape_string($this->connectionlink,$data['AllDispositionBinID'])."','".mysqli_real_escape_string($this->connectionlink,$data['AllDispositionBinName'])."',SerialNumber,UniversalModelNumber,'Fail-VirtualSerialBinMismatch','".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."','".mysqli_real_escape_string($this->connectionlink,$row['BinName'])."',NOW(),'".$_SESSION['user']['UserId']."',AssetScanID,disposition_id,'".mysqli_real_escape_string($this->connectionlink,$data['SourceDispositionName'])."','".$event_id."','".$batch_event_flag."' from asset where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."'
            ";
            $q4 = mysqli_query($this->connectionlink,$query4);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            //End insert in bin_audit_records


            //Start update Asset
            $query = "update asset set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."',DateUpdated = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."' ";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }

            $query1 = "update custompallet_items set CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."' and AssetScanID > 0";
            $q1 = mysqli_query($this->connectionlink,$query1);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }

            //End update Asset

            //Start update CustomPallet counts
            $query6 = "select count(*) from custompallet_items where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."' ";
            $q6 = mysqli_query($this->connectionlink,$query6);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row6 = mysqli_fetch_assoc($q6);
                $query2 = "update custompallet set AssetsCount = '".$row6['count(*)']."',LastModifiedDate = NOW(),LastModifiedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$row['CustomPalletID'])."'";
                $q2 = mysqli_query($this->connectionlink,$query2);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
            }
            

            $query6 = "select count(*) from custompallet_items where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."' ";
            $q6 = mysqli_query($this->connectionlink,$query6);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row6 = mysqli_fetch_assoc($q6);
                $query2 = "update custompallet set AssetsCount = '".$row6['count(*)']."',LastModifiedDate = NOW(),LastModifiedBy = '".$_SESSION['user']['UserId']."' where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."'";
                $q2 = mysqli_query($this->connectionlink,$query2);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
            }           
            //End update CustomPallet counts

            //Start update about audit complete
            $query7 = "update bin_audit_bins set AuditLocked = 0,AuditedDate = NOW(),AuditedBy = '".$_SESSION['user']['UserId']."',Status = 'Unlocked' where ID = '".mysqli_real_escape_string($this->connectionlink,$data['BinAuditID'])."'";
            $q7 = mysqli_query($this->connectionlink,$query7);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }

            $query8 = "update custompallet set AuditDate = NOW(),AuditLocked = 0,StatusID = 1 where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."'";
            $q8 = mysqli_query($this->connectionlink,$query8);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            //End update about audit complete

            //Start check If Control is Target or Schedule
            $query9 = "select * from bin_audit_controls where ControlID = '".mysqli_real_escape_string($this->connectionlink,$data['ControlID'])."'";
            $q9 = mysqli_query($this->connectionlink,$query9);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row9 = mysqli_fetch_assoc($q9);
                if($row9['Type'] == 'Target') {
                    $query10 = "update bin_audit_controls set Status = 'Completed',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where ControlID = '".mysqli_real_escape_string($this->connectionlink,$data['ControlID'])."'";
                    $q10 = mysqli_query($this->connectionlink,$query10);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }
                }
            }
            //End check If Control is Target or Schedule

            $json['Success'] = true;
			$json['Result'] = 'Bin Audit Completed';
            return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}


    public function CompleteBinAudit($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data
			);            
            //return json_encode($json);
            if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Audit Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Bin Audit Page';
				return json_encode($json);
			}

            //Start update about audit complete
            $query7 = "update bin_audit_bins set AuditLocked = 0,AuditedDate = NOW(),AuditedBy = '".$_SESSION['user']['UserId']."',Status = 'Unlocked' where ID = '".mysqli_real_escape_string($this->connectionlink,$data['BinAuditID'])."'";
            $q7 = mysqli_query($this->connectionlink,$query7);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }

            $query8 = "update custompallet set AuditDate = NOW(),AuditLocked = 0,StatusID = 1 where CustomPalletID = '".mysqli_real_escape_string($this->connectionlink,$data['SourceBinID'])."'";
            $q8 = mysqli_query($this->connectionlink,$query8);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            //End update about audit complete


            //Start check If Control is Target or Schedule
            $query9 = "select * from bin_audit_controls where ControlID = '".mysqli_real_escape_string($this->connectionlink,$data['ControlID'])."'";
            $q9 = mysqli_query($this->connectionlink,$query9);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }
            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row9 = mysqli_fetch_assoc($q9);
                if($row9['Type'] == 'Target') {
                    $query10 = "update bin_audit_controls set Status = 'Completed',UpdatedDate = NOW(),UpdatedBy = '".$_SESSION['user']['UserId']."' where ControlID = '".mysqli_real_escape_string($this->connectionlink,$data['ControlID'])."'";
                    $q10 = mysqli_query($this->connectionlink,$query10);
                    if(mysqli_error($this->connectionlink)) {
                        $json['Success'] = false;
                        $json['Result'] = mysqli_error($this->connectionlink);
                        return json_encode($json);
                    }
                }
            }
            //End check If Control is Target or Schedule

            $json['Success'] = true;
			$json['Result'] = 'Bin Audit Completed';
            return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

    public function GetEvaluationResult($data) {
		try {
			if(!isset($_SESSION['user'])) {
				$json['Success'] = false;
				$json['Result'] = 'Login to continue';
				return json_encode($json);
			}
			$json = array(
				'Success' => false,
				'Result' => $data
			);            
            //return json_encode($json);
            if(! $this->isPermitted($_SESSION['user']['ProfileID'],'Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'No Access to Bin Audit Page';
				return json_encode($json);
			}

            if(! $this->isWritePermitted($_SESSION['user']['ProfileID'],'Bin Audit')) {
				$json['Success'] = false;
				$json['Result'] = 'You have Read only Access to Bin Audit Page';
				return json_encode($json);
			}
            $query = "select * from bin_audit_evaluation_results_combinations where SerialNumberStatus = '".mysqli_real_escape_string($this->connectionlink,$data['SerialNumberStatus'])."' and SerialInBinStatus = '".mysqli_real_escape_string($this->connectionlink,$data['SerialInBinStatus'])."' and MPNStatus = '".mysqli_real_escape_string($this->connectionlink,$data['MPNStatus'])."' and DamageStatus = '".mysqli_real_escape_string($this->connectionlink,$data['Damaged'])."' and DispositionType = '".mysqli_real_escape_string($this->connectionlink,$data['DispositionType'])."' and  Status = 'Active'";
            $q = mysqli_query($this->connectionlink,$query);
            if(mysqli_error($this->connectionlink)) {
                $json['Success'] = false;
                $json['Result'] = mysqli_error($this->connectionlink);
                return json_encode($json);
            }

            if(mysqli_affected_rows($this->connectionlink) > 0) {
                $row = mysqli_fetch_assoc($q);

                $query22 = "select a.*,d.disposition,d.color_code from asset a 
                left join disposition d on a.disposition_id = d.disposition_id 
                where a.AssetScanID = '".mysqli_real_escape_string($this->connectionlink,$data['AssetScanID'])."' ";
                $q22 = mysqli_query($this->connectionlink,$query22);
                if(mysqli_error($this->connectionlink)) {
                    $json['Success'] = false;
                    $json['Result'] = mysqli_error($this->connectionlink);
                    return json_encode($json);
                }
                if(mysqli_affected_rows($this->connectionlink) > 0) {
                    $row22 = mysqli_fetch_assoc($q22);
                    $row['AssetDispositionID'] = $row22['disposition_id'];
                    $row['AssetDisposition'] = $row22['disposition'];
                } else {
                    $json['Success'] = false;
                    $json['Result'] = 'Invalid Serial';
                    return json_encode($json);
                }

                $json['Success'] = true;
                $json['Result'] = $row;
                return json_encode($json);
            } else {
                $json['Success'] = false;
                $json['Result'] = 'No Evaluation Result Available';
                return json_encode($json);
            }
            
            return json_encode($json);
		} catch (Exception $ex) {
			$json['Success'] = false;
			$json['Result'] = $ex->getMessage();
			return json_encode($json);
		}
	}

}
?>