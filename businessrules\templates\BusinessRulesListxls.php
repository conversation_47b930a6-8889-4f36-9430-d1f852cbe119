<?php
session_start();
include_once("../../config.php");
$data = $_SESSION['BusinessRuleListxls'];
require_once("xlsxwriter.class.php");
require_once('xlsxwriterplus.class.php');
ini_set('display_errors', 0);
ini_set('log_errors', 1);
error_reporting(E_ALL & ~E_NOTICE);
$today = date("m-d-Y");
$data1 = array('Date',$today);
setlocale(LC_MONETARY, 'en_US.UTF-8');
$filename = "BusinessRuleList.xlsx";
header('Content-disposition: attachment; filename="'.XLSXWriter::sanitize_filename($filename).'"');
header("Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
header('Content-Transfer-Encoding: binary');
header('Cache-Control: must-revalidate');
header('Pragma: public');
include_once("../../connection.php");
setlocale(LC_MONETARY, 'de_DE.UTF-8');
$obj1 =  new Connection();
$connectionlink = Connection::DBConnect();
$datatoday = array('Generated Date',$today);
$datahead = array('BusinessRule List');
// Updated header to match list page order: Version Name, Priority, Rule Name, Description, Customer, Facility, Workflow, Part Type, Rule Summary, Disposition, Rule ID, Source Type, Material Type, Status, Created Date, Created By, Updated Date, Updated By
$header = array('Version Name','Priority','Rule Name','Description','Customer','Facility','Workflow','Part Type','Rule Summary','Disposition','Rule ID','Source Type','Material Type','Status','Created Date','Created By','Updated Date','Updated By');

            // Handle multiple version IDs
            $version_condition = "";
            if(isset($data['version_ids_array']) && is_array($data['version_ids_array']) && count($data['version_ids_array']) > 0) {
                $escaped_versions = array();
                foreach($data['version_ids_array'] as $version_id) {
                    $escaped_versions[] = "'".mysqli_real_escape_string($connectionlink, trim($version_id))."'";
                }
                $version_condition = "r.version_id IN (" . implode(',', $escaped_versions) . ")";
            } else {
                // Fallback to single version for backward compatibility
                $version_condition = "r.version_id = '".mysqli_real_escape_string($connectionlink,$data['version_id'])."'";
            }

            $query = "select r.*,f.FacilityName,d.disposition,sd.disposition as sub_disposition,v.version_name,v.current_version,v.status as VersionStatus,w.workflow,ac.Customer as CustomerName,ct.Cumstomertype as SourceTypeName,cu.FirstName as CreatedByFirstName,cu.LastName as CreatedByLastName,uu.FirstName as UpdatedByFirstName,uu.LastName as UpdatedByLastName from business_rule r
			left join facility f on r.FacilityID = f.FacilityID
			left join disposition d on d.disposition_id = r.disposition_id
			left join disposition sd on r.sub_disposition_id = sd.disposition_id
			left join business_rule_versions v on r.version_id = v.version_id
			left join workflow w on r.workflow_id = w.workflow_id
			left join aws_customers ac on r.AWSCustomerID = ac.AWSCustomerID
			left join customertype ct on r.idCustomertype = ct.idCustomertype
			left join users cu on r.created_by = cu.UserId
			left join users uu on r.updated_by = uu.UserId
			WHERE " . $version_condition . " AND r.status != 'Archived'";

          if($data[0] && count($data[0]) > 0) {
                foreach ($data[0] as $key => $value) {
                    if($value != '') {
                        if($key == 'rule_id_text') {
                            $query = $query . " AND r.rule_id_text like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
                        }
                        if($key == 'rule_name') {
                            $query = $query . " AND r.rule_name like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
                        }
                        if($key == 'rule_description') {
                            $query = $query . " AND r.rule_description like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
                        }
                        if($key == 'FacilityName') {
                            $query = $query . " AND f.FacilityName like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
                        }
                        if($key == 'priority') {
                            $query = $query . " AND r.priority like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
                        }
                        if($key == 'CustomerName') {
                            // Handle Customer filtering for both single and comma-separated values
                            if($value == 'All') {
                                $query = $query . " AND (r.AWSCustomerID = 'all' OR r.AWSCustomerID = 'All') ";
                            } else {
                                $query = $query . " AND (ac.Customer like '%".mysqli_real_escape_string($connectionlink,$value)."%' OR r.AWSCustomerID like '%".mysqli_real_escape_string($connectionlink,$value)."%') ";
                            }
                        }

                        if($key == 'disposition') {
                            $query = $query . " AND d.disposition like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
                        }
                        if($key == 'rule_summary') {
                            $query = $query . " AND r.rule_summary like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
                        }
                        if($key == 'sub_disposition') {
							$query = $query . " AND sd.disposition like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
						}
						if($key == 'status') {
							$query = $query . " AND r.status like '%".mysqli_real_escape_string($connectionlink,$value)."%' ";
						}
                    }
                }
            }

            // Always order by version first to group rules by version, then apply user sorting within each version
            if($data['OrderBy'] != '') {
                if($data['OrderByType'] == 'asc') {
                    $order_by_type = 'asc';
                } else {
                    $order_by_type = 'desc';
                }

                if($data['OrderBy'] == 'CustomerName') {
					$query = $query . " order by v.current_version DESC, v.version_name ASC, ac.Customer ".$order_by_type." ";
				} else if($data['OrderBy'] == 'FacilityName') {
					$query = $query . " order by v.current_version DESC, v.version_name ASC, f.FacilityName ".$order_by_type." ";
				} else if($data['OrderBy'] == 'workflow') {
					$query = $query . " order by v.current_version DESC, v.version_name ASC, w.workflow ".$order_by_type." ";
				} else if($data['OrderBy'] == 'part_types') {
					$query = $query . " order by v.current_version DESC, v.version_name ASC, r.part_types ".$order_by_type." ";
				} else if($data['OrderBy'] == 'rule_id_text') {
					$query = $query . " order by v.current_version DESC, v.version_name ASC, r.rule_id_text ".$order_by_type." ";
				} else if($data['OrderBy'] == 'rule_name') {
					$query = $query . " order by v.current_version DESC, v.version_name ASC, r.rule_name ".$order_by_type." ";
				} else if($data['OrderBy'] == 'rule_description') {
					$query = $query . " order by v.current_version DESC, v.version_name ASC, r.rule_description ".$order_by_type." ";
				} else if($data['OrderBy'] == 'priority') {
					$query = $query . " order by v.current_version DESC, v.version_name ASC, r.priority ".$order_by_type." ";
				} else if($data['OrderBy'] == 'disposition') {
					$query = $query . " order by v.current_version DESC, v.version_name ASC, d.disposition ".$order_by_type." ";
				} else if($data['OrderBy'] == 'rule_summary') {
					$query = $query . " order by v.current_version DESC, v.version_name ASC, r.rule_summary ".$order_by_type." ";
				} else if($data['OrderBy'] == 'SourceTypeName') {
					$query = $query . " order by v.current_version DESC, v.version_name ASC, ct.Cumstomertype ".$order_by_type." ";
				} else if($data['OrderBy'] == 'MaterialType') {
					$query = $query . " order by v.current_version DESC, v.version_name ASC, r.MaterialType ".$order_by_type." ";
				} else if($data['OrderBy'] == 'status') {
					$query = $query . " order by v.current_version DESC, v.version_name ASC, r.status ".$order_by_type." ";
				} else if($data['OrderBy'] == 'version_name') {
					$query = $query . " order by v.current_version DESC, v.version_name ".$order_by_type." ";
				} else if($data['OrderBy'] == 'created_date') {
					$query = $query . " order by v.current_version DESC, v.version_name ASC, r.created_date ".$order_by_type." ";
				} else if($data['OrderBy'] == 'created_by') {
					$query = $query . " order by v.current_version DESC, v.version_name ASC, CONCAT(cu.FirstName, ' ', cu.LastName) ".$order_by_type." ";
				} else if($data['OrderBy'] == 'updated_date') {
					$query = $query . " order by v.current_version DESC, v.version_name ASC, r.updated_date ".$order_by_type." ";
				} else if($data['OrderBy'] == 'updated_by') {
					$query = $query . " order by v.current_version DESC, v.version_name ASC, CONCAT(uu.FirstName, ' ', uu.LastName) ".$order_by_type." ";
				}
            } else {
                // Default ordering: Current version first, then by version name, then by priority within each version
                $query = $query . " order by v.current_version DESC, v.version_name ASC, r.priority ASC ";
            }
$sql = mysqli_query($connectionlink,$query);
if(mysqli_error($connectionlink)) {
    echo mysqli_error($connectionlink);
}
while($row = mysqli_fetch_assoc($sql))
{
    // Handle "All" values and comma-separated Customer IDs for display
    if($row['AWSCustomerID'] == 'all' || $row['AWSCustomerID'] == 'All') {
        $customer = 'All';
    } else if(strpos($row['AWSCustomerID'], ',') !== false) {
        // Handle comma-separated Customer IDs - convert to customer names
        $customerIds = explode(',', $row['AWSCustomerID']);
        $customerNames = array();
        foreach($customerIds as $customerId) {
            $customerId = trim($customerId);
            if(!empty($customerId)) {
                // Query to get customer name for each ID
                $customerQuery = "SELECT Customer FROM aws_customers WHERE AWSCustomerID = '".mysqli_real_escape_string($connectionlink, $customerId)."'";
                $customerResult = mysqli_query($connectionlink, $customerQuery);
                if($customerResult && mysqli_num_rows($customerResult) > 0) {
                    $customerRow = mysqli_fetch_assoc($customerResult);
                    $customerNames[] = $customerRow['Customer'];
                } else {
                    $customerNames[] = $customerId; // Fallback to ID if name not found
                }
            }
        }
        $customer = implode(', ', $customerNames);
    } else {
        // Single Customer ID
        $customer = $row['CustomerName'] ?: $row['AWSCustomerID'];
    }
    $facility = ($row['FacilityID'] == 'all' || $row['FacilityID'] == 'All') ? 'All' : $row['FacilityName'];
    $workflow = ($row['workflow_id'] == 'all' || $row['workflow_id'] == 'All') ? 'All' : $row['workflow'];
    $sourceType = ($row['idCustomertype'] == 'all' || $row['idCustomertype'] == 'All') ? 'All' : $row['SourceTypeName'];

    // Add "Current Version" text to version name if it's the current version
    $versionName = $row['version_name'];
    if($row['current_version'] == '1') {
        $versionName .= ' (Current Version)';
    }

    // Format user names
    $createdBy = '';
    if($row['CreatedByFirstName'] && $row['CreatedByLastName']) {
        $createdBy = $row['CreatedByFirstName'] . ' ' . $row['CreatedByLastName'];
    } else {
        $createdBy = $row['created_by'] ?: '';
    }

    $updatedBy = '';
    if($row['UpdatedByFirstName'] && $row['UpdatedByLastName']) {
        $updatedBy = $row['UpdatedByFirstName'] . ' ' . $row['UpdatedByLastName'];
    } else {
        $updatedBy = $row['updated_by'] ?: '';
    }

    // Format dates
    $createdDate = $row['created_date'] ? date('m/d/Y H:i', strtotime($row['created_date'])) : '';
    $updatedDate = $row['updated_date'] ? date('m/d/Y H:i', strtotime($row['updated_date'])) : '';

    if($row['MaterialType'] == 'all') {
        $row['MaterialType'] = 'All';
    }

    // Updated row order to match header: Version Name, Priority, Rule Name, Description, Customer, Facility, Workflow, Part Type, Rule Summary, Disposition, Rule ID, Source Type, Material Type, Status, Created Date, Created By, Updated Date, Updated By
    $row2  = array($versionName,$row['priority'],$row['rule_name'],$row['rule_description'],$customer,$facility,$workflow,$row['part_types'],$row['rule_summary'],$row['disposition'],$row['rule_id_text'],$sourceType,$row['MaterialType'],$row['status'],$createdDate,$createdBy,$updatedDate,$updatedBy);
    $rows[] = $row2;
}

$sheet_name = 'BusinessRuleList';
$style1 = array( ['font-style'=>'bold'],['font-style'=>'']);
$writer = new XLSWriterPlus();
$writer->setAuthor('eViridis');
$writer->writeSheetRow($sheet_name, $header, $col_options = ['font-style'=>'bold', 'border'=>'left,right,top,bottom','halign'=>'center','valign'=>'center','fill'=>'#eee']);
foreach($rows as $row11)
    $writer->writeSheetRow($sheet_name, $row11 , $col_options = ['border'=>'left,right,top,bottom','halign'=>'left']);
$writer->writeToStdOut();
exit(0);
?>